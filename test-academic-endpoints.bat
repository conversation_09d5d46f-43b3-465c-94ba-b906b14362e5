@echo off
echo Testing Student Academic Endpoints...
echo.

set BASE_URL=http://student-api.localhost/api/student
set STUDENT_ID=ST254917
set AUTH_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.lCkaQ0zLTR12_CicRkGHgXnQD8FLhu1xSUr-fD0i6qY

echo 1. Testing connectivity...
curl -I "%BASE_URL%" --connect-timeout 5
if %ERRORLEVEL% neq 0 (
    echo ❌ Services are not running. Please start Docker services first:
    echo    docker compose up -d postgres redis rabbitmq students-service student-apigw traefik
    echo.
    pause
    exit /b 1
)

echo.
echo 2. Testing POST /api/student/%STUDENT_ID%/academic (Create/Update Academic Data)...
curl --location "%BASE_URL%/%STUDENT_ID%/academic" ^
--header "Content-Type: application/json" ^
--header "Authorization: Bearer %AUTH_TOKEN%" ^
--data "{\"academic\":[{\"foreignDegree\":false,\"nameOfExam\":\"HSC\",\"institute\":\"Dhaka College\",\"subject\":\"Science\",\"board\":\"Dhaka\",\"grade\":\"A+\",\"passingYear\":\"2013\"},{\"foreignDegree\":false,\"nameOfExam\":\"SSC\",\"institute\":\"Dhaka College\",\"subject\":\"Science\",\"board\":\"Dhaka\",\"grade\":\"A+\",\"passingYear\":\"2011\"}],\"proficiency\":[{\"nameOfExam\":\"IELTS\",\"score\":{\"overall\":7.5,\"R\":8.0,\"L\":7.5,\"W\":7.0,\"S\":7.5},\"examDate\":\"2023-06-15\",\"expiryDate\":\"2025-06-15\",\"note\":\"Academic module\"}],\"publications\":[{\"subject\":\"Machine Learning\",\"journal\":\"IEEE Transactions\",\"publicationDate\":\"2023-01-15\",\"link\":\"https://ieee.org/paper123\"}],\"otherActivities\":[{\"subject\":\"Web Development\",\"certificationLink\":\"https://cert.example.com/web-dev\",\"startDate\":\"2023-01-01\",\"endDate\":\"2023-06-30\"}]}"

echo.
echo.
echo 3. Testing GET /api/student/%STUDENT_ID%/academic (Retrieve Academic Data)...
curl --location "%BASE_URL%/%STUDENT_ID%/academic" ^
--header "Authorization: Bearer %AUTH_TOKEN%"

echo.
echo.
echo Tests completed!
pause
