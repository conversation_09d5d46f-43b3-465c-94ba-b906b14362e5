import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { AuditModel } from './audit.model';
import { AuditRepository } from './audit.repository';
import { AuditClientService } from './audit-client.service';

@Module({
  imports: [
    SequelizeModule.forFeature([AuditModel]),
  ],
  providers: [
    AuditRepository,
    AuditClientService,
  ],
  exports: [
    AuditRepository,
    AuditClientService,
  ],
})
export class AuditModule {}
