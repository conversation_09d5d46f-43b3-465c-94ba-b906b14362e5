# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy source code
COPY apps/core/audit-logging ./apps/core/audit-logging
COPY libs ./libs

# Install dependencies
RUN npm ci

# Build the application
RUN npx nx build audit-logging --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist/apps/core/audit-logging ./

# Install production dependencies
RUN npm ci --only=production

# Install curl for healthcheck
RUN apk --no-cache add curl

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3001
ENV RUN_MIGRATIONS=true

# Expose the service port
EXPOSE 3001

# Add healthcheck that verifies the service is up after migrations
HEALTHCHECK --interval=10s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start the service
CMD ["node", "main.js"]

