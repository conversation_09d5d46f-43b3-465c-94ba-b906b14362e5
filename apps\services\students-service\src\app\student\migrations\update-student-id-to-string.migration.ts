import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // Update all student-related tables to change studentId from BIGINT to STRING
  
  // 1. Update student_academic_records table
  await queryInterface.changeColumn('student_academic_records', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 2. Update student_proficiency_records table
  await queryInterface.changeColumn('student_proficiency_records', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 3. Update student_publications table
  await queryInterface.changeColumn('student_publications', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 4. Update student_social_links table
  await queryInterface.changeColumn('student_social_links', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 5. Update student_other_activities table
  await queryInterface.changeColumn('student_other_activities', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 6. Update student_academic_backgrounds table
  await queryInterface.changeColumn('student_academic_backgrounds', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 7. Update student_personal_info table
  await queryInterface.changeColumn('student_personal_info', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 8. Update enrollments table
  await queryInterface.changeColumn('enrollments', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 9. Update grades table
  await queryInterface.changeColumn('grades', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 10. Update emergency_contacts table
  await queryInterface.changeColumn('emergency_contacts', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });

  // 11. Update student_documents table
  await queryInterface.changeColumn('student_documents', 'studentId', {
    type: DataTypes.STRING,
    allowNull: false
  });
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // Revert all changes back to BIGINT
  
  // 1. Revert student_academic_records table
  await queryInterface.changeColumn('student_academic_records', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 2. Revert student_proficiency_records table
  await queryInterface.changeColumn('student_proficiency_records', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 3. Revert student_publications table
  await queryInterface.changeColumn('student_publications', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 4. Revert student_social_links table
  await queryInterface.changeColumn('student_social_links', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 5. Revert student_other_activities table
  await queryInterface.changeColumn('student_other_activities', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 6. Revert student_academic_backgrounds table
  await queryInterface.changeColumn('student_academic_backgrounds', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 7. Revert student_personal_info table
  await queryInterface.changeColumn('student_personal_info', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 8. Revert enrollments table
  await queryInterface.changeColumn('enrollments', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 9. Revert grades table
  await queryInterface.changeColumn('grades', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 10. Revert emergency_contacts table
  await queryInterface.changeColumn('emergency_contacts', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });

  // 11. Revert student_documents table
  await queryInterface.changeColumn('student_documents', 'studentId', {
    type: DataTypes.BIGINT,
    allowNull: false
  });
}; 