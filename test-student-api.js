const axios = require('axios');

// Configuration
const BASE_URL = 'http://student-api.localhost/api/student';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4XrTXtHcnqWOeD8da58qBL1ZQ0fxafqSpe31CkZ_-zA';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bear<PERSON> ${AUTH_TOKEN}`
};

// Test data
const testStudentData = {
  organizationId: 1,
  agencyId: null,
  firstName: "<PERSON>",
  lastName: "Doe",
  nameInNative: "জন ডো",
  email: "<EMAIL>",
  phone: "+8801234567890",
  guardianPhone: "+8801234567891",
  dateOfBirth: "1995-01-15",
  gender: "male",
  fatherName: "<PERSON>",
  motherName: "<PERSON>",
  nid: "1234567890123",
  passport: "*********",
  presentAddress: {
    address: "123 Main St",
    country: "Bangladesh",
    state: "Dhaka",
    city: "Dhaka",
    postalCode: "1000"
  },
  permanentAddress: {
    address: "456 Home St",
    country: "Bangladesh",
    state: "Dhaka",
    city: "Dhaka",
    postalCode: "1000"
  },
  maritalStatus: {
    status: "single",
    spouseName: "",
    spousePhone: "",
    spousePassport: ""
  },
  sponsor: {
    name: "<PERSON>",
    relation: "father",
    phone: "+8801234567891",
    email: "<EMAIL>"
  },
  emergencyContact: {
    name: "Jane Doe",
    relation: "mother",
    phone: "+8801234567892",
    email: "<EMAIL>"
  },
  preferredSubject: ["Computer Science", "Engineering"],
  preferredCountry: ["Canada", "Australia"],
  socialLinks: [],
  reference: "Online",
  note: "Test student for API testing"
};

const testAcademicData = {
  academic: [
    {
      foreignDegree: false,
      nameOfExam: "HSC",
      institute: "Dhaka College",
      subject: "Science",
      board: "Dhaka",
      grade: "A+",
      passingYear: "2013"
    },
    {
      foreignDegree: false,
      nameOfExam: "SSC",
      institute: "Dhaka College",
      subject: "Science",
      board: "Dhaka",
      grade: "A+",
      passingYear: "2011"
    }
  ],
  proficiency: [
    {
      nameOfExam: "IELTS",
      score: {
        overall: 7.5,
        R: 8.0,
        L: 7.5,
        W: 7.0,
        S: 7.5
      },
      examDate: "2023-06-15",
      expiryDate: "2025-06-15",
      note: "Academic module"
    }
  ],
  publications: [
    {
      subject: "Machine Learning",
      journal: "IEEE Transactions",
      publicationDate: "2023-01-15",
      link: "https://ieee.org/paper123"
    }
  ],
  otherActivities: {
    subject: "Web Development",
    certificationLink: "https://cert.example.com/web-dev",
    startDate: "2023-01-01",
    endDate: "2023-06-30"
  }
};

async function testAPI() {
  try {
    console.log('🚀 Starting Student API Tests...\n');

    // Test 1: Create a student
    console.log('1️⃣ Testing Student Creation...');
    try {
      const createResponse = await axios.post(BASE_URL, testStudentData, { headers });
      console.log('✅ Student created successfully');
      console.log('Student ID:', createResponse.data?.student?.studentId || 'Not found');
      
      const studentId = createResponse.data?.student?.studentId;
      if (!studentId) {
        console.log('❌ No student ID returned, cannot continue with academic tests');
        return;
      }

      // Test 2: Add academic information
      console.log('\n2️⃣ Testing Academic Information Creation...');
      const academicResponse = await axios.post(
        `${BASE_URL}/${studentId}/academic`,
        testAcademicData,
        { headers }
      );
      console.log('✅ Academic information added successfully');
      console.log('Response:', JSON.stringify(academicResponse.data, null, 2));

      // Test 3: Get academic information
      console.log('\n3️⃣ Testing Academic Information Retrieval...');
      const getAcademicResponse = await axios.get(
        `${BASE_URL}/${studentId}/academic`,
        { headers }
      );
      console.log('✅ Academic information retrieved successfully');
      console.log('Academic Data:', JSON.stringify(getAcademicResponse.data, null, 2));

      // Test 4: List students
      console.log('\n4️⃣ Testing Student List...');
      const listResponse = await axios.get(BASE_URL, { headers });
      console.log('✅ Students listed successfully');
      console.log('Total students:', listResponse.data?.total || 'Not found');

    } catch (error) {
      console.log('❌ Error in student creation:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the tests
testAPI();
