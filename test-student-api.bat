@echo off
echo Testing Student API Endpoints...
echo.

set BASE_URL=http://student-api.localhost/api/student
set AUTH_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4XrTXtHcnqWOeD8da58qBL1ZQ0fxafqSpe31CkZ_-zA

echo 1. Testing Student Academic Endpoint (Original Request)...
curl --location "%BASE_URL%/ST254224/academic" ^
--header "Content-Type: application/json" ^
--header "Authorization: Bearer %AUTH_TOKEN%" ^
--data "{\"academic\":[{\"foreignDegree\":false,\"nameOfExam\":\"HSC\",\"institute\":\"Dhaka College\",\"subject\":\"Science\",\"board\":\"Dhaka\",\"grade\":\"A+\",\"passingYear\":\"2013\"},{\"foreignDegree\":false,\"nameOfExam\":\"SSC\",\"institute\":\"Dhaka College\",\"subject\":\"Science\",\"board\":\"Dhaka\",\"grade\":\"A+\",\"passingYear\":\"2011\"}],\"proficiency\":[{\"nameOfExam\":\"IELTS\",\"score\":{\"overall\":7.5,\"R\":8.0,\"L\":7.5,\"W\":7.0,\"S\":7.5},\"examDate\":\"2023-06-15\",\"expiryDate\":\"2025-06-15\",\"note\":\"Academic module\"}],\"publications\":[{\"subject\":\"Machine Learning\",\"journal\":\"IEEE Transactions\",\"publicationDate\":\"2023-01-15\",\"link\":\"https://ieee.org/paper123\"}],\"otherActivities\":{\"subject\":\"Web Development\",\"certificationLink\":\"https://cert.example.com/web-dev\",\"startDate\":\"2023-01-01\",\"endDate\":\"2023-06-30\"}}"

echo.
echo 2. Testing Student List Endpoint...
curl --location "%BASE_URL%" ^
--header "Authorization: Bearer %AUTH_TOKEN%"

echo.
echo 3. Testing Student Creation...
curl --location "%BASE_URL%" ^
--header "Content-Type: application/json" ^
--header "Authorization: Bearer %AUTH_TOKEN%" ^
--data "{\"organizationId\":1,\"firstName\":\"Test\",\"lastName\":\"Student\",\"email\":\"<EMAIL>\",\"phone\":\"+8801234567890\",\"dateOfBirth\":\"1995-01-15\",\"gender\":\"male\"}"

echo.
echo Tests completed!
pause
