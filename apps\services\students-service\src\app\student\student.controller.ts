import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { StudentService } from './student.service';
import { status } from '@grpc/grpc-js';

@Controller()
export class StudentController {
  private readonly logger = new Logger(StudentController.name);

  constructor(private readonly studentService: StudentService) {}

  @GrpcMethod('StudentService', 'CreateStudent')
  async createStudent(data: any): Promise<any> {
    try {
      this.logger.log('Creating student:', data);
      const student = await this.studentService.createStudent(data);
      return { student };
    } catch (error) {
      this.logger.error('Error creating student:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to create student'
      });
    }
  }

  @GrpcMethod('StudentService', 'CreateStudentAfterRegistration')
  async createStudentAfterRegistration(data: any): Promise<any> {
    try {
      this.logger.log('Creating student after registration completion:', data);

      // Validate required fields for post-registration student creation
      if (!data.email) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Missing required field: email'
        });
      }

      // Parse name field if it contains full name
      let firstName = data.first_name || '';
      let lastName = data.last_name || '';

      if (data.name && !firstName && !lastName) {
        const nameParts = data.name.trim().split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
      }

      const student = await this.studentService.createStudent(data);

      this.logger.log(
        `Student created successfully after registration: ${student.id}`
      );
      return { student };
    } catch (error) {
      this.logger.error('Error creating student after registration:', error);

      // Handle specific error cases
      if (error.message?.includes('already exists')) {
        throw new RpcException({
          code: status.ALREADY_EXISTS,
          message: 'Student with this email already exists'
        });
      }

      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to create student after registration'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetStudent')
  async getStudent(data: { id: string }): Promise<any> {
    try {
      this.logger.log('Getting student:', data.id);
      const student = await this.studentService.getStudent(data.id);
      return { student };
    } catch (error) {
      this.logger.error('Error getting student:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get student'
      });
    }
  }

  @GrpcMethod('StudentService', 'CheckStudentExistsByEmail')
  async checkStudentExistsByEmail(data: { email: string }): Promise<any> {
    try {
      this.logger.log('Checking if student exists by email:', data.email);
      const student = await this.studentService.checkStudentExistsByEmail(
        data.email
      );
      return {
        exists: !!student,
        student: student || null
      };
    } catch (error) {
      this.logger.error('Error checking student by email:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to check student by email'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetStudentByEmail')
  async getStudentByEmail(data: { email: string }): Promise<any> {
    try {
      this.logger.log('Getting student by email:', data.email);
      const student = await this.studentService.getStudentByEmail(data.email);
      if (!student) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Student not found'
        });
      }
      return { student };
    } catch (error) {
      this.logger.error('Error getting student by email:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get student by email'
      });
    }
  }

  @GrpcMethod('StudentService', 'UpdateStudent')
  async updateStudent(data: { id: string; student: any }): Promise<any> {
    try {
      this.logger.log('Updating student:', data.id);
      const student = await this.studentService.updateStudent(
        data.id,
        data.student
      );
      return { student };
    } catch (error) {
      this.logger.error('Error updating student:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to update student'
      });
    }
  }

  @GrpcMethod('StudentService', 'DeleteStudent')
  async deleteStudent(data: { id: string }): Promise<any> {
    try {
      this.logger.log('Deleting student:', data.id);
      await this.studentService.deleteStudent(data.id);
      return { success: true };
    } catch (error) {
      this.logger.error('Error deleting student:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to delete student'
      });
    }
  }

  @GrpcMethod('StudentService', 'ListStudents')
  async listStudents(data: any): Promise<any> {
    try {
      this.logger.log('Listing students with filters:', data);
      const result = await this.studentService.listStudents(data);
      return result;
    } catch (error) {
      this.logger.error('Error listing students:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to list students'
      });
    }
  }

  @GrpcMethod('StudentService', 'EnrollInCourse')
  async enrollInCourse(data: {
    studentId: string;
    course_id: string;
    semester: string;
    course_data?: any;
  }): Promise<any> {
    try {
      this.logger.log('Enrolling student in course:', data);
      const enrollment = await this.studentService.enrollInCourse(
        data.studentId,
        data.course_id,
        data.semester,
        data.course_data
      );
      return { enrollment };
    } catch (error) {
      this.logger.error('Error enrolling in course:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : error.message?.includes('already enrolled')
        ? status.ALREADY_EXISTS
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to enroll in course'
      });
    }
  }

  @GrpcMethod('StudentService', 'DropCourse')
  async dropCourse(data: {
    studentId: string;
    course_id: string;
    semester: string;
  }): Promise<any> {
    try {
      this.logger.log('Dropping course:', data);
      await this.studentService.dropCourse(
        data.studentId,
        data.course_id,
        data.semester
      );
      return { success: true };
    } catch (error) {
      this.logger.error('Error dropping course:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to drop course'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetEnrollments')
  async getEnrollments(data: {
    studentId: string;
    semester?: string;
  }): Promise<any> {
    try {
      this.logger.log('Getting enrollments:', data);
      const enrollments = await this.studentService.getEnrollments(
        data.studentId,
        data.semester
      );
      return { enrollments };
    } catch (error) {
      this.logger.error('Error getting enrollments:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to get enrollments'
      });
    }
  }

  @GrpcMethod('StudentService', 'UpdateGrades')
  async updateGrades(data: {
    studentId: string;
    course_id: string;
    grade_data: any;
  }): Promise<any> {
    try {
      this.logger.log('Updating grades:', data);
      const grade = await this.studentService.updateGrades(
        data.studentId,
        data.course_id,
        data.grade_data
      );
      return { grade };
    } catch (error) {
      this.logger.error('Error updating grades:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to update grades'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetAcademicProgress')
  async getAcademicProgress(data: { studentId: string }): Promise<any> {
    try {
      this.logger.log('Getting academic progress:', data.studentId);
      const progress = await this.studentService.getAcademicProgress(
        data.studentId
      );
      return { progress };
    } catch (error) {
      this.logger.error('Error getting academic progress:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get academic progress'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetTranscript')
  async getTranscript(data: { studentId: string }): Promise<any> {
    try {
      this.logger.log('Getting transcript:', data.studentId);
      const transcript = await this.studentService.getTranscript(
        data.studentId
      );
      return { transcript };
    } catch (error) {
      this.logger.error('Error getting transcript:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get transcript'
      });
    }
  }

  @GrpcMethod('StudentService', 'CreateOrUpdateStudentAcademic')
  async createOrUpdateStudentAcademic(data: any): Promise<any> {
    try {
      this.logger.log('Creating/updating student academic info:', data);
      const result = await this.studentService.createOrUpdateStudentAcademic(
        data
      );
      return {
        success: true,
        message: 'Student academic information saved successfully',
        data: result
      };
    } catch (error) {
      this.logger.error(
        'Error creating/updating student academic info:',
        error
      );
      throw new RpcException({
        code: status.INTERNAL,
        message:
          error.message || 'Failed to create/update student academic info'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetStudentAcademic')
  async getStudentAcademic(data: any): Promise<any> {
    try {
      this.logger.log('Getting student academic info for:', data.studentId);
      const result = await this.studentService.getStudentAcademic(
        data.studentId
      );
      return {
        success: true,
        message: 'Student academic information retrieved successfully',
        data: result
      };
    } catch (error) {
      this.logger.error('Error getting student academic info:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get student academic info'
      });
    }
  }
}
