{"name": "apply-goal-backend-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["apply-goal-backend"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/apply-goal-backend-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["apply-goal-backend:build"]}}}