[{"role": "Super Admin", "department": [{"name": "Admin"}], "modules": [{"module": "Dashboard Access", "features": [{"feature": "Overview of key metrics", "permissions": true, "subFeatures": []}, {"feature": "Quick links to important actions", "permissions": true, "subFeatures": []}, {"feature": "Notifications and reminders", "permissions": true, "subFeatures": []}]}, {"module": "Users Management", "features": [{"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": true, "subFeatures": []}, {"feature": "Deactivate", "permissions": true, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "View Activity", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": true, "subFeatures": [{"subFeature": "Assign role and permissions", "permissions": true}, {"subFeature": "Assign a Team", "permissions": true}]}]}, {"module": "Leads Management", "features": [{"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Follow-up Tracking", "permissions": true, "subFeatures": []}, {"feature": "Assign leades to user", "permissions": true, "subFeatures": []}, {"feature": "lead Conversion to application", "permissions": true, "subFeatures": []}]}, {"module": "Application Management", "features": [{"feature": "Track application status", "permissions": true, "subFeatures": []}, {"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": true, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": true, "subFeatures": [{"subFeature": "Documents Upload", "permissions": true}, {"subFeature": "Documents Download", "permissions": true}, {"subFeature": "Documents Delete", "permissions": true}, {"subFeature": "Documents View", "permissions": true}, {"subFeature": "Documents Edit", "permissions": true}, {"subFeature": "ESL Start Date (Change)", "permissions": true}, {"subFeature": "Intake (Change)", "permissions": true}, {"subFeature": "Documents Main Copy (Check/Uncheck)", "permissions": true}, {"subFeature": "Docuements Review (Approve/Reject)", "permissions": true}, {"subFeature": "Documents Resubmission", "permissions": true}, {"subFeature": "Notes Against Applications", "permissions": true}]}]}, {"module": "Financial Transaction", "features": [{"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": true, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Track payments", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": true, "subFeatures": [{"subFeature": "Manage refunds and discounts", "permissions": true}, {"subFeature": "Financial reporting", "permissions": true}, {"subFeature": "Send Invoices", "permissions": true}]}]}, {"module": "HR feature Access", "features": [{"feature": "Employee records", "permissions": true, "subFeatures": []}, {"feature": "Attendance tracking", "permissions": true, "subFeatures": []}, {"feature": "Leave management", "permissions": true, "subFeatures": []}, {"feature": "Role assignments", "permissions": true, "subFeatures": []}]}, {"module": "System Settings", "features": [{"feature": "CRM configuration", "permissions": true, "subFeatures": []}, {"feature": "Role and permission setup", "permissions": true, "subFeatures": []}, {"feature": "Employee management", "permissions": true, "subFeatures": []}, {"feature": "Notification preferences", "permissions": true, "subFeatures": []}, {"feature": "Backup and security settings", "permissions": true, "subFeatures": []}]}, {"module": "Reports & Analytics", "features": [{"feature": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": true, "subFeatures": []}, {"feature": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"feature": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"module": "Agency Management", "features": [{"feature": "Onboard new agency partners", "permissions": true, "subFeatures": []}, {"feature": "Track agency performance", "permissions": true, "subFeatures": []}, {"feature": "View applications submitted by agencies", "permissions": true, "subFeatures": []}, {"feature": "Set commission rates", "permissions": true, "subFeatures": []}]}, {"module": "University Management", "features": [{"feature": "Add/update university profiles", "permissions": true, "subFeatures": []}, {"feature": "Manage university requirements", "permissions": true, "subFeatures": []}, {"feature": "Application intake calendars", "permissions": true, "subFeatures": []}, {"feature": "Course list and program info", "permissions": true, "subFeatures": []}, {"feature": "Request University for Onboarding", "permissions": true, "subFeatures": []}]}, {"module": "Student Profile Management", "features": [{"feature": "Communication logs", "permissions": true, "subFeatures": []}, {"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": true, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": true, "subFeatures": [{"subFeature": "Store student's personal info", "permissions": true}, {"subFeature": "Education history", "permissions": true}]}]}, {"module": "Support & Maintenance / IT/ System Admins", "features": [{"feature": "Submit support tickets", "permissions": true, "subFeatures": []}, {"feature": "IT activity log", "permissions": true, "subFeatures": []}, {"feature": "System status checks", "permissions": true, "subFeatures": []}, {"feature": "Maintenance and Setup", "permissions": true, "subFeatures": []}]}, {"module": "Task Management", "features": [{"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": true, "subFeatures": []}, {"feature": "Track task status and progress", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": true, "subFeatures": [{"subFeature": "Assign task", "permissions": true}, {"subFeature": "Set deadlines and priorities", "permissions": true}, {"subFeature": "Link tasks to students, leads, or applications", "permissions": true}]}]}]}, {"role": "University Admin", "department": "Admin", "modules": [{"module": "Dashboard Access", "features": [{"feature": "Overview of key metrics", "permissions": false, "subFeatures": []}, {"feature": "Quick links to important actions", "permissions": false, "subFeatures": []}, {"feature": "Notifications and reminders", "permissions": false, "subFeatures": []}]}, {"module": "Users Management", "features": [{"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "Deactivate", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "View Activity", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Leads Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"feature": "Assign leades to user", "permissions": false, "subFeatures": []}, {"feature": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"module": "Application Management", "features": [{"feature": "Track application status", "permissions": true, "subFeatures": []}, {"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Financial Transaction", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "Track payments", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "HR feature Access", "features": [{"feature": "Employee records", "permissions": false, "subFeatures": []}, {"feature": "Attendance tracking", "permissions": false, "subFeatures": []}, {"feature": "Leave management", "permissions": false, "subFeatures": []}, {"feature": "Role assignments", "permissions": false, "subFeatures": []}]}, {"module": "System Settings", "features": [{"feature": "CRM configuration", "permissions": false, "subFeatures": []}, {"feature": "Role and permission setup", "permissions": false, "subFeatures": []}, {"feature": "Notification preferences", "permissions": false, "subFeatures": []}, {"feature": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"module": "Reports & Analytics", "features": [{"feature": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": false, "subFeatures": []}, {"feature": "Export to PDF/Excel", "permissions": false, "subFeatures": []}, {"feature": "Dashboard widgets with filters", "permissions": false, "subFeatures": []}]}, {"module": "Agency Management", "features": [{"feature": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"feature": "Track agency performance", "permissions": false, "subFeatures": []}, {"feature": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"feature": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"module": "University Management", "features": [{"feature": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"feature": "Manage university requirements", "permissions": false, "subFeatures": []}, {"feature": "Application intake calendars", "permissions": true, "subFeatures": []}, {"feature": "Course list and program info", "permissions": true, "subFeatures": []}, {"feature": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"module": "Student Profile Management", "features": [{"feature": "Communication logs", "permissions": false, "subFeatures": []}, {"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Support & Maintenance / IT/ System Admins", "features": [{"feature": "Submit support tickets", "permissions": false, "subFeatures": []}, {"feature": "IT activity log", "permissions": false, "subFeatures": []}, {"feature": "System status checks", "permissions": false, "subFeatures": []}, {"feature": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"module": "Task Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "Track task status and progress", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}]}, {"role": "Student", "department": "Student", "modules": [{"module": "Dashboard Access", "features": [{"feature": "Overview of key metrics", "permissions": true, "subFeatures": []}, {"feature": "Quick links to important actions", "permissions": true, "subFeatures": []}, {"feature": "Notifications and reminders", "permissions": true, "subFeatures": []}]}, {"module": "Users Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "Deactivate", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "View Activity", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Leads Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"feature": "Assign leades to user", "permissions": false, "subFeatures": []}, {"feature": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"module": "Application Management", "features": [{"feature": "Track application status", "permissions": true, "subFeatures": []}, {"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Financial Transaction", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "Track payments", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "HR feature Access", "features": [{"feature": "Employee records", "permissions": false, "subFeatures": []}, {"feature": "Attendance tracking", "permissions": false, "subFeatures": []}, {"feature": "Leave management", "permissions": false, "subFeatures": []}, {"feature": "Role assignments", "permissions": false, "subFeatures": []}]}, {"module": "System Settings", "features": [{"feature": "CRM configuration", "permissions": false, "subFeatures": []}, {"feature": "Role and permission setup", "permissions": false, "subFeatures": []}, {"feature": "Notification preferences", "permissions": false, "subFeatures": []}, {"feature": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"module": "Reports & Analytics", "features": [{"feature": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": false, "subFeatures": []}, {"feature": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"feature": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"module": "Agency Management", "features": [{"feature": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"feature": "Track agency performance", "permissions": false, "subFeatures": []}, {"feature": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"feature": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"module": "University Management", "features": [{"feature": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"feature": "Manage university requirements", "permissions": false, "subFeatures": []}, {"feature": "Application intake calendars", "permissions": false, "subFeatures": []}, {"feature": "Course list and program info", "permissions": false, "subFeatures": []}, {"feature": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"module": "Student Profile Management", "features": [{"feature": "Communication logs", "permissions": true, "subFeatures": []}, {"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": true, "subFeatures": []}]}, {"module": "Support & Maintenance / IT/ System Admins", "features": [{"feature": "Submit support tickets", "permissions": false, "subFeatures": []}, {"feature": "IT activity log", "permissions": false, "subFeatures": []}, {"feature": "System status checks", "permissions": false, "subFeatures": []}, {"feature": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"module": "Task Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "Track task status and progress", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}]}, {"role": "Agency Admin", "department": "Agency", "modules": [{"module": "Dashboard Access", "features": [{"feature": "Overview of key metrics", "permissions": false, "subFeatures": []}, {"feature": "Quick links to important actions", "permissions": false, "subFeatures": []}, {"feature": "Notifications and reminders", "permissions": false, "subFeatures": []}]}, {"module": "Users Management", "features": [{"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": true, "subFeatures": []}, {"feature": "Deactivate", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "View Activity", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Leads Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"feature": "Assign leades to user", "permissions": false, "subFeatures": []}, {"feature": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"module": "Application Management", "features": [{"feature": "Track application status", "permissions": true, "subFeatures": []}, {"feature": "Create", "permissions": true, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Financial Transaction", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "Track payments", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "HR feature Access", "features": [{"feature": "Employee records", "permissions": true, "subFeatures": []}, {"feature": "Attendance tracking", "permissions": true, "subFeatures": []}, {"feature": "Leave management", "permissions": true, "subFeatures": []}, {"feature": "Role assignments", "permissions": true, "subFeatures": []}]}, {"module": "System Settings", "features": [{"feature": "CRM configuration", "permissions": false, "subFeatures": []}, {"feature": "Role and permission setup", "permissions": true, "subFeatures": []}, {"feature": "Notification preferences", "permissions": false, "subFeatures": []}, {"feature": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"module": "Reports & Analytics", "features": [{"feature": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": false, "subFeatures": []}, {"feature": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"feature": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"module": "Agency Management", "features": [{"feature": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"feature": "Track agency performance", "permissions": true, "subFeatures": []}, {"feature": "View applications submitted by agencies", "permissions": true, "subFeatures": []}, {"feature": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"module": "University Management", "features": [{"feature": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"feature": "Manage university requirements", "permissions": false, "subFeatures": []}, {"feature": "Application intake calendars", "permissions": false, "subFeatures": []}, {"feature": "Course list and program info", "permissions": false, "subFeatures": []}, {"feature": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"module": "Student Profile Management", "features": [{"feature": "Communication logs", "permissions": false, "subFeatures": []}, {"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Support & Maintenance / IT/ System Admins", "features": [{"feature": "Submit support tickets", "permissions": false, "subFeatures": []}, {"feature": "IT activity log", "permissions": false, "subFeatures": []}, {"feature": "System status checks", "permissions": false, "subFeatures": []}, {"feature": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"module": "Task Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "Track task status and progress", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}]}, {"role": "Agency Student", "department": "Agency", "modules": [{"module": "Dashboard Access", "features": [{"feature": "Overview of key metrics", "permissions": false, "subFeatures": []}, {"feature": "Quick links to important actions", "permissions": false, "subFeatures": []}, {"feature": "Notifications and reminders", "permissions": false, "subFeatures": []}]}, {"module": "Users Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "Deactivate", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "View Activity", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Leads Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"feature": "Assign leades to user", "permissions": false, "subFeatures": []}, {"feature": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"module": "Application Management", "features": [{"feature": "Track application status", "permissions": true, "subFeatures": []}, {"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Financial Transaction", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": false, "subFeatures": []}, {"feature": "Track payments", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "HR feature Access", "features": [{"feature": "Employee records", "permissions": false, "subFeatures": []}, {"feature": "Attendance tracking", "permissions": false, "subFeatures": []}, {"feature": "Leave management", "permissions": false, "subFeatures": []}, {"feature": "Role assignments", "permissions": false, "subFeatures": []}]}, {"module": "System Settings", "features": [{"feature": "CRM configuration", "permissions": false, "subFeatures": []}, {"feature": "Role and permission setup", "permissions": false, "subFeatures": []}, {"feature": "Notification preferences", "permissions": false, "subFeatures": []}, {"feature": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"module": "Reports & Analytics", "features": [{"feature": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": false, "subFeatures": []}, {"feature": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"feature": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"module": "Agency Management", "features": [{"feature": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"feature": "Track agency performance", "permissions": false, "subFeatures": []}, {"feature": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"feature": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"module": "University Management", "features": [{"feature": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"feature": "Manage university requirements", "permissions": false, "subFeatures": []}, {"feature": "Application intake calendars", "permissions": false, "subFeatures": []}, {"feature": "Course list and program info", "permissions": false, "subFeatures": []}, {"feature": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"module": "Student Profile Management", "features": [{"feature": "Communication logs", "permissions": false, "subFeatures": []}, {"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "View", "permissions": true, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}, {"module": "Support & Maintenance / IT/ System Admins", "features": [{"feature": "Submit support tickets", "permissions": false, "subFeatures": []}, {"feature": "IT activity log", "permissions": false, "subFeatures": []}, {"feature": "System status checks", "permissions": false, "subFeatures": []}, {"feature": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"module": "Task Management", "features": [{"feature": "Create", "permissions": false, "subFeatures": []}, {"feature": "Delete", "permissions": false, "subFeatures": []}, {"feature": "Track task status and progress", "permissions": false, "subFeatures": []}, {"feature": "Edit", "permissions": false, "subFeatures": []}]}]}]