{"name": "identity-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/core/identity-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "identity-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "identity-service:build:development"}, "production": {"buildTarget": "identity-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}