import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { MigrationService } from './app/migration/migration.service';
import { Logger } from '@nestjs/common';

async function runMigration() {
  const app = await NestFactory.create(AppModule);
  const migrationService = app.get(MigrationService);
  const logger = new Logger('MigrationScript');

  try {
    logger.log('Starting migration to update studentId columns...');
    
    // Run the migration service which will handle both sync and migrations
    await migrationService.migrate();
    
    logger.log('Migration completed successfully!');
  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

runMigration(); 