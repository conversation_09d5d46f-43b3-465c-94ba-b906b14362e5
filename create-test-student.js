const { Client } = require('@grpc/grpc-js');
const { loadPackageDefinition } = require('@grpc/grpc-js');
const { loadSync } = require('@grpc/proto-loader');
const path = require('path');

// Load the proto file
const PROTO_PATH = path.join(__dirname, 'libs/shared/dto/src/lib/students/students.proto');
const packageDefinition = loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true
});

const protoDescriptor = loadPackageDefinition(packageDefinition);
const students = protoDescriptor.students;

// Create client
const client = new students.StudentService(
  'localhost:50058',
  require('@grpc/grpc-js').credentials.createInsecure()
);

// Create a test student
function createTestStudent() {
  const studentData = {
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    date_of_birth: {
      year: 1995,
      month: 6,
      day: 15
    },
    major: 'Computer Science',
    address: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA'
    }
  };

  return new Promise((resolve, reject) => {
    client.createStudent(studentData, (error, response) => {
      if (error) {
        console.error('Error creating student:', error);
        reject(error);
      } else {
        console.log('Student created:', JSON.stringify(response, null, 2));
        resolve(response);
      }
    });
  });
}

// Run the test
async function runTest() {
  try {
    console.log('Creating test student...');
    const result = await createTestStudent();
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

runTest(); 