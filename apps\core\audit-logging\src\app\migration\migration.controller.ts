import { <PERSON>, Post, Get } from '@nestjs/common';
import { MigrationService } from './migration.service';

@Controller('migrate')
export class MigrationController {
  constructor(private readonly migrationService: MigrationService) {}

  @Post()
  async migrate() {
    await this.migrationService.migrate();
    return { message: 'Migration completed successfully' };
  }

  @Post('sync')
  async sync() {
    await this.migrationService.syncDatabase();
    return { message: 'Database sync completed successfully' };
  }

  @Get('status')
  async getMigrationStatus() {
    const status = await this.migrationService.getMigrationStatus();
    return { status };
  }
}
