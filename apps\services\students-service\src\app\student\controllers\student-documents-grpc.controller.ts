import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { MulterFile } from '@apply-goal-backend/common';
import { StudentDocumentsService } from '../services/student-documents.service';
import {
  CreateStudentDocumentsDto,
  GetStudentDocumentsQueryDto,
  UpdateDocumentVerificationDto,
  DependentDto,
  ChildDto,
  SponsorMetadataDto
} from '../dto/create-student-documents.dto';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

interface FileUpload {
  fieldname: string;
  filename: string;
  mimetype: string;
  data: Buffer;
  size: number;
}

interface UploadDocumentsRequest {
  studentId: string;
  sponsor_name: string;
  take_dependents: boolean;
  academic_sections: string[];
  proficiency_sections: string[];
  sponsor_metadata?: {
    name: string;
    relationship?: string;
    phone?: string;
    email?: string;
  };
  dependents?: Array<{ name: string; passport: string }>;
  children?: Array<{ name: string; passport: string }>;
  files: FileUpload[];
}

interface GetStudentDocumentsRequest {
  studentId: string;
  section?: string;
  field?: string;
  verification_status?: string;
  include_inactive?: boolean;
}

interface GetDocumentRequest {
  studentId: string;
  document_id: number;
}

interface UpdateDocumentVerificationRequest {
  document_id: number;
  verification_status: string;
  notes?: string;
  expiry_date?: string;
}

interface DeleteDocumentRequest {
  studentId: string;
  document_id: number;
}

interface GetDocumentsSummaryRequest {
  studentId: string;
}

@Controller()
export class StudentDocumentsGrpcController {
  private readonly logger = new Logger(StudentDocumentsGrpcController.name);

  constructor(
    private readonly studentDocumentsService: StudentDocumentsService
  ) {}

  @GrpcMethod('StudentDocumentsService', 'UploadDocuments')
  async uploadDocuments(request: UploadDocumentsRequest) {
    try {
      this.logger.log(
        `gRPC: Uploading documents for student ${request.studentId}`
      );

      // Convert gRPC request to DTO
      const dto: CreateStudentDocumentsDto = {
        studentId: request.studentId,
        sponsorName: request.sponsor_name,
        takeDependents: request.take_dependents,
        academicSections: request.academic_sections || [],
        proficiencySections: request.proficiency_sections || [],
        sponsorMetadata: request.sponsor_metadata as SponsorMetadataDto,
        dependents: request.dependents as DependentDto[],
        children: request.children as ChildDto[]
      };

      // Convert files to MulterFile format
      const filesObject: { [fieldname: string]: MulterFile[] } = {};

      for (const file of request.files || []) {
        const multerFile: MulterFile = {
          fieldname: file.fieldname,
          originalname: file.filename,
          encoding: '7bit',
          mimetype: file.mimetype,
          size: file.size,
          buffer: file.data
        };

        if (!filesObject[file.fieldname]) {
          filesObject[file.fieldname] = [];
        }
        filesObject[file.fieldname].push(multerFile);
      }

      const result = await this.studentDocumentsService.createOrUpdate(
        request.studentId,
        dto,
        filesObject
      );

      return {
        success: result.success,
        message: result.message,
        documents: result.documents.map((doc) => this.mapToGrpcDocument(doc)),
        total_uploaded: result.totalUploaded,
        failed_uploads: result.failedUploads,
        errors: result.errors || []
      };
    } catch (error) {
      this.logger.error(`gRPC: Failed to upload documents:`, error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message
      });
    }
  }

  @GrpcMethod('StudentDocumentsService', 'GetStudentDocuments')
  async getStudentDocuments(request: GetStudentDocumentsRequest) {
    try {
      this.logger.log(
        `gRPC: Getting documents for student ${request.studentId}`
      );

      const query: GetStudentDocumentsQueryDto = {
        section: request.section,
        field: request.field,
        verificationStatus: request.verification_status as any,
        includeInactive: request.include_inactive
      };

      const documents = await this.studentDocumentsService.getStudentDocuments(
        request.studentId,
        query
      );

      return {
        documents: documents.map((doc) => this.mapToGrpcDocument(doc))
      };
    } catch (error) {
      this.logger.error(`gRPC: Failed to get documents:`, error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message
      });
    }
  }

  @GrpcMethod('StudentDocumentsService', 'GetDocument')
  async getDocument(request: GetDocumentRequest) {
    try {
      this.logger.log(
        `gRPC: Getting document ${request.document_id} for student ${request.studentId}`
      );

      const documents = await this.studentDocumentsService.getStudentDocuments(
        request.studentId,
        {}
      );

      const document = documents.find((doc) => doc.id === request.document_id);

      if (!document) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: `Document ${request.document_id} not found for student ${request.studentId}`
        });
      }

      return {
        document: this.mapToGrpcDocument(document)
      };
    } catch (error) {
      this.logger.error(`gRPC: Failed to get document:`, error);
      if (error instanceof RpcException) {
        throw error;
      }
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message
      });
    }
  }

  @GrpcMethod('StudentDocumentsService', 'UpdateDocumentVerification')
  async updateDocumentVerification(request: UpdateDocumentVerificationRequest) {
    try {
      this.logger.log(
        `gRPC: Updating verification for document ${request.document_id}`
      );

      const updateDto: UpdateDocumentVerificationDto = {
        verificationStatus: request.verification_status as any,
        notes: request.notes,
        expiryDate: request.expiry_date
          ? new Date(request.expiry_date)
          : undefined
      };

      const document =
        await this.studentDocumentsService.updateDocumentVerification(
          request.document_id,
          updateDto
        );

      return {
        document: this.mapToGrpcDocument(document)
      };
    } catch (error) {
      this.logger.error(`gRPC: Failed to update document verification:`, error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message
      });
    }
  }

  @GrpcMethod('StudentDocumentsService', 'DeleteDocument')
  async deleteDocument(request: DeleteDocumentRequest) {
    try {
      this.logger.log(
        `gRPC: Deleting document ${request.document_id} for student ${request.studentId}`
      );

      await this.studentDocumentsService.deleteDocument(request.document_id);

      return {
        message: 'Document deleted successfully'
      };
    } catch (error) {
      this.logger.error(`gRPC: Failed to delete document:`, error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message
      });
    }
  }

  @GrpcMethod('StudentDocumentsService', 'GetDocumentsSummary')
  async getDocumentsSummary(request: GetDocumentsSummaryRequest) {
    try {
      this.logger.log(
        `gRPC: Getting documents summary for student ${request.studentId}`
      );

      const documents = await this.studentDocumentsService.getStudentDocuments(
        request.studentId,
        {}
      );

      const sections: { [key: string]: any } = {};

      for (const doc of documents) {
        if (!sections[doc.section]) {
          sections[doc.section] = {
            total: 0,
            verified: 0,
            pending: 0,
            rejected: 0,
            fields: []
          };
        }

        const section = sections[doc.section];
        section.total++;

        if (doc.verificationStatus === 'verified') section.verified++;
        else if (doc.verificationStatus === 'rejected') section.rejected++;
        else section.pending++;

        if (!section.fields.includes(doc.field)) {
          section.fields.push(doc.field);
        }
      }

      return { sections };
    } catch (error) {
      this.logger.error(`gRPC: Failed to get documents summary:`, error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message
      });
    }
  }

  private mapToGrpcDocument(doc: any) {
    return {
      id: doc.id,
      studentId: doc.studentId,
      section: doc.section,
      field: doc.field,
      filename: doc.filename,
      url: doc.url,
      mime_type: doc.mimeType || '',
      file_size: doc.fileSize || 0,
      original_name: doc.originalName || '',
      bucket: doc.bucket || '',
      object_key: doc.objectKey || '',
      metadata: doc.metadata
        ? {
            dependent_index: doc.metadata.dependentIndex,
            child_index: doc.metadata.childIndex,
            dependent_name: doc.metadata.dependentName,
            child_name: doc.metadata.childName,
            dependent_passport: doc.metadata.dependentPassport,
            child_passport: doc.metadata.childPassport,
            additional_data: doc.metadata.additionalData || {}
          }
        : undefined,
      is_active: true,
      verification_status: doc.verificationStatus || 'pending',
      expiry_date: doc.expiryDate ? doc.expiryDate.toISOString() : undefined,
      notes: doc.notes,
      created_at: doc.created_at.toISOString(),
      updated_at: doc.updated_at.toISOString()
    };
  }
}
