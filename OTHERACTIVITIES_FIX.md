# Fix for otherActivities Array Issue

## Problem
The API was returning a 500 error with the message:
```
{
    "statusCode": 500,
    "message": "13 INTERNAL: Request message serialization failure: .students.CreateOrUpdateStudentAcademicRequest.otherActivities: array expected"
}
```

## Root Cause
The gRPC protobuf definition expected `otherActivities` to be an array (`repeated StudentOtherActivity`), but:

1. **Gateway Service**: Was transforming it to a single object
2. **Database Model**: Was expecting a single object
3. **Interface Definition**: Was defined as a single object

## Files Fixed

### 1. `apps/gateways/student-apigw/src/app/student.controller.ts`
**Changed**: Interface definition for `StudentAcademicRequest`
```typescript
// Before
otherActivities: {
  subject: string;
  certificationLink: string;
  startDate: string;
  endDate: string;
};

// After  
otherActivities: Array<{
  subject: string;
  certificationLink: string;
  startDate: string;
  endDate: string;
}>;
```

### 2. `apps/gateways/student-apigw/src/app/student.service.ts`
**Changed**: Transformation logic in `transformAcademicToProtoFormat`
```typescript
// Before
otherActivities: {
  subject: academicData.otherActivities?.subject || '',
  certification_link: academicData.otherActivities?.certificationLink || '',
  start_date: academicData.otherActivities?.startDate || '',
  end_date: academicData.otherActivities?.endDate || ''
}

// After
otherActivities: (academicData.otherActivities || []).map((activity: any) => ({
  subject: activity.subject || '',
  certification_link: activity.certificationLink || '',
  start_date: activity.startDate || '',
  end_date: activity.endDate || ''
}))
```

### 3. `apps/services/students-service/src/app/student/models/student-academic.model.ts`
**Changed**: Database model definition
```typescript
// Before
// Other Activities (JSON object)
@Column({
  type: DataType.JSON,
  allowNull: true,
  defaultValue: {}
})
otherActivities: {
  subject: string;
  certification_link: string;
  start_date: string;
  end_date: string;
};

// After
// Other Activities (JSON array)
@Column({
  type: DataType.JSON,
  allowNull: true,
  defaultValue: []
})
otherActivities: Array<{
  subject: string;
  certification_link: string;
  start_date: string;
  end_date: string;
}>;
```

### 4. `apps/services/students-service/src/app/student/student.service.ts`
**Changed**: Default value in service method
```typescript
// Before
otherActivities: academicData.otherActivities || {}

// After
otherActivities: academicData.otherActivities || []
```

### 5. `test-student-api.js`
**Updated**: Test data structure
```javascript
// Before
otherActivities: {
  subject: "Web Development",
  certificationLink: "https://cert.example.com/web-dev",
  startDate: "2023-01-01",
  endDate: "2023-06-30"
}

// After
otherActivities: [
  {
    subject: "Web Development", 
    certificationLink: "https://cert.example.com/web-dev",
    startDate: "2023-01-01",
    endDate: "2023-06-30"
  }
]
```

## Expected Request Format
The API now correctly expects `otherActivities` as an array:

```json
{
  "academic": [...],
  "proficiency": [...],
  "publications": [...],
  "otherActivities": [
    {
      "subject": "Web Development",
      "certificationLink": "https://cert.example.com/web-dev", 
      "startDate": "2023-01-01",
      "endDate": "2023-06-30"
    }
  ]
}
```

## Test Command
The corrected curl command should now work:

```bash
curl --location 'http://student-api.localhost/api/student/ST255292/academic' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer [TOKEN]' \
--data '{
    "academic": [...],
    "proficiency": [...], 
    "publications": [...],
    "otherActivities": [
        {
            "subject": "Web Development",
            "certificationLink": "https://cert.example.com/web-dev",
            "startDate": "2023-01-01", 
            "endDate": "2023-06-30"
        }
    ]
}'
```

This fix ensures consistency between the API interface, gRPC protobuf definition, database model, and transformation logic.
