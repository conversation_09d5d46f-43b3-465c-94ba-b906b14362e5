# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy source code
COPY apps/core/identity-service ./apps/core/identity-service
COPY libs ./libs

# Install dependencies
RUN npm ci

# Build the application
RUN npx nx build identity-service --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist/apps/core/identity-service ./

# Install production dependencies
RUN npm ci --only=production

# Install curl for healthcheck
RUN apk --no-cache add curl

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3002

# Expose the service port
EXPOSE 3002

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3002/health || exit 1

# Start the service
CMD ["node", "main.js"]

