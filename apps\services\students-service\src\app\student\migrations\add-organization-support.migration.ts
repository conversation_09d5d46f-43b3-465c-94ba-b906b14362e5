import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 1. Add organizationId to students table
  await queryInterface.addColumn('students', 'organizationId', {
    type: DataTypes.BIGINT,
    allowNull: false,
    references: {
      model: 'organizations',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
    comment:
      'Organization ID - always required (ApplyGoal or agency organization)'
  });

  // 3. Update agency_id comment
  await queryInterface.changeColumn('students', 'agency_id', {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Agency ID - optional, null if student not under any agency'
  });

  // 2. Add index for organizationId
  await queryInterface.addIndex('students', ['organizationId']);

  // 3. Note: ApplyGoal organization should already exist in auth-service
  // If not, it will be created by the auth-service seed
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 1. Remove organizationId from students table
  await queryInterface.removeColumn('students', 'organizationId');

  // 2. Note: organizations table is managed by auth-service
};
