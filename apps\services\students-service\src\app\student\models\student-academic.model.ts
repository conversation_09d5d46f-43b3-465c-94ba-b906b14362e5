import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
  CreatedAt,
  UpdatedAt
} from 'sequelize-typescript';
import { Student } from './student.model';
import { BaseModel } from '@apply-goal-backend/database';

@Table({
  tableName: 'student_academic_backgrounds',
  timestamps: true
})
export class StudentAcademicBackground extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    references: {
      model: 'students',
      key: 'id'
    }
  })
  studentId: bigint;

  @BelongsTo(() => Student, { foreignKey: 'studentId', targetKey: 'id' })
  student: Student;

  // Academic Records (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  academic_records: Array<{
    foreign_degree: boolean;
    name_of_exam: string;
    institute: string;
    subject: string;
    board: string;
    grade: string;
    passing_year: string;
  }>;

  // Proficiency Records (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  proficiencyRecords: Array<{
    name_of_exam: string;
    score: {
      overall: number;
      r: number;
      l: number;
      w: number;
      s: number;
    };
    exam_date: string;
    expiry_date: string;
    note: string;
  }>;

  // Publication Records (JSON array)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  publicationRecords: Array<{
    subject: string;
    journal: string;
    publication_date: string;
    link: string;
  }>;

  // Other Activities (JSON object)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: {}
  })
  otherActivities: {
    subject: string;
    certification_link: string;
    start_date: string;
    end_date: string;
  };
}
