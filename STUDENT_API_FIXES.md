# Student API Issues and Fixes

## Issues Identified

### 1. Data Structure Mismatch
**Problem**: The API gateway expected different field names than what the service was configured to handle.
- Gateway expected: `academic` 
- Service expected: `academic_records`

**Fix**: Updated the transformation method in `apps/gateways/student-apigw/src/app/student.service.ts` to use correct field names.

### 2. Field Name Inconsistencies
**Problem**: Request used camelCase but model expected snake_case
- Request: `foreignDegree`, `nameOfExam`, etc.
- Model: `foreign_degree`, `name_of_exam`, etc.

**Fix**: Maintained the transformation in the gateway service to convert between formats.

### 3. Proficiency Score Field Mismatch
**Problem**: Request used uppercase letters (`R`, `L`, `W`, `S`) but model expected lowercase (`r`, `l`, `w`, `s`).

**Fix**: Updated transformation to convert `R` → `r`, `L` → `l`, etc.

### 4. Missing Data Transformation
**Problem**: The gateway service had commented out transformation logic, causing raw data to be sent to the service.

**Fix**: Uncommented and corrected the `transformAcademicToProtoFormat` method.

### 5. Database Model Inconsistency
**Problem**: The service was trying to find students by `studentId` in the academic background table, but the foreign key references the student's internal `id`.

**Fix**: Updated the service to:
1. Find student by `studentId` first
2. Use the student's internal `id` for academic background operations

## Files Modified

### 1. `apps/gateways/student-apigw/src/app/student.service.ts`
- Uncommented and fixed the `transformAcademicToProtoFormat` method
- Updated field names to match the service expectations:
  - `academic` → `academic_records`
  - `proficiency` → `proficiencyRecords`
  - `publications` → `publicationRecords`
  - `other_activities` → `otherActivities`
- Fixed proficiency score field mapping

### 2. `apps/services/students-service/src/app/student/student.service.ts`
- Updated `createOrUpdateStudentAcademic` method to:
  - Add better logging for debugging
  - Use correct foreign key relationships
  - Handle data structure properly
  - Provide better error messages
- Fixed `getStudentAcademic` method to use correct foreign key

## Test Files Created

### 1. `test-student-api.js`
Node.js test script that:
- Creates a test student
- Adds academic information
- Retrieves academic information
- Lists students

### 2. `test-student-api.bat`
Windows batch script with curl commands for testing:
- Student academic endpoint
- Student list endpoint
- Student creation endpoint

## Expected API Flow

1. **Student Creation**: `POST /api/student`
2. **Add Academic Info**: `POST /api/student/{studentId}/academic`
3. **Get Academic Info**: `GET /api/student/{studentId}/academic`
4. **List Students**: `GET /api/student`

## Data Structure

### Request Format (Academic Endpoint)
```json
{
  "academic": [
    {
      "foreignDegree": false,
      "nameOfExam": "HSC",
      "institute": "Dhaka College",
      "subject": "Science",
      "board": "Dhaka",
      "grade": "A+",
      "passingYear": "2013"
    }
  ],
  "proficiency": [
    {
      "nameOfExam": "IELTS",
      "score": {
        "overall": 7.5,
        "R": 8.0,
        "L": 7.5,
        "W": 7.0,
        "S": 7.5
      },
      "examDate": "2023-06-15",
      "expiryDate": "2025-06-15",
      "note": "Academic module"
    }
  ],
  "publications": [
    {
      "subject": "Machine Learning",
      "journal": "IEEE Transactions",
      "publicationDate": "2023-01-15",
      "link": "https://ieee.org/paper123"
    }
  ],
  "otherActivities": {
    "subject": "Web Development",
    "certificationLink": "https://cert.example.com/web-dev",
    "startDate": "2023-01-01",
    "endDate": "2023-06-30"
  }
}
```

### Transformed Format (Service)
```json
{
  "studentId": "ST254224",
  "academic_records": [...],
  "proficiencyRecords": [...],
  "publicationRecords": [...],
  "otherActivities": {...}
}
```

## Next Steps

1. Start the required Docker services:
   - postgres
   - redis
   - students-service
   - student-apigw

2. Run the test scripts to verify the fixes

3. Check logs for any remaining issues

## Services Required

```bash
# Start infrastructure
docker compose up -d postgres redis

# Start core services
docker compose up -d students-service

# Start API gateway
docker compose up -d student-apigw
```
