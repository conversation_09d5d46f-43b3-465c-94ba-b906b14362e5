import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout, retry } from 'rxjs/operators';
import {
  CreateAuditLogRequest,
  GetAuditLogRequest,
  ListAuditLogsRequest,
  SearchAuditLogsRequest,
  ListAuditLogsResponse,
  AuditLogResponse,
} from './interfaces/audit.interface';

interface AuditService {
  createAuditLog(request: CreateAuditLogRequest): Observable<AuditLogResponse>;
  getAuditLog(request: GetAuditLogRequest): Observable<AuditLogResponse>;
  listAuditLogs(request: ListAuditLogsRequest): Observable<ListAuditLogsResponse>;
  searchAuditLogs(request: SearchAuditLogsRequest): Observable<ListAuditLogsResponse>;
}

@Injectable()
export class AuditClientService implements OnModuleInit {
  private readonly logger = new Logger(AuditClientService.name);

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'audit',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/audit/audit.proto'),
      url: 'audit-logging:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private auditService: AuditService;

  onModuleInit() {
    this.auditService = this.client.getService<AuditService>('AuditService');
  }

  private handleError(operation = 'operation') {
    return (error: any) => {
      this.logger.error(`${operation} failed: ${error.message}`, error.stack);
      return throwError(() => error);
    };
  }

  createAuditLog(request: CreateAuditLogRequest): Observable<AuditLogResponse> {
    return this.auditService.createAuditLog(request).pipe(
      timeout(5000),
      retry(3),
      catchError(this.handleError('createAuditLog'))
    );
  }

  getAuditLog(request: GetAuditLogRequest): Observable<AuditLogResponse> {
    return this.auditService.getAuditLog(request).pipe(
      timeout(5000),
      retry(3),
      catchError(this.handleError('getAuditLog'))
    );
  }

  listAuditLogs(request: ListAuditLogsRequest): Observable<ListAuditLogsResponse> {
    return this.auditService.listAuditLogs(request).pipe(
      timeout(5000),
      retry(3),
      catchError(this.handleError('listAuditLogs'))
    );
  }

  searchAuditLogs(request: SearchAuditLogsRequest): Observable<ListAuditLogsResponse> {
    return this.auditService.searchAuditLogs(request).pipe(
      timeout(5000),
      retry(3),
      catchError(this.handleError('searchAuditLogs'))
    );
  }
}
