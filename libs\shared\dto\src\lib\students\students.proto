syntax = "proto3";

package students;

// Import google protobuf types for Timestamp
import "google/protobuf/timestamp.proto";

service StudentService {
  // Student CRUD Operations
  rpc CreateStudent(CreateStudentRequest) returns (StudentResponse) {}
  rpc CreateStudentAfterRegistration(CreateStudentAfterRegistrationRequest)
      returns (StudentResponse) {}
  rpc GetStudent(GetStudentRequest) returns (StudentResponse) {}
  rpc ListStudents(ListStudentsRequest) returns (ListStudentsResponse) {}
  rpc CreateOrUpdateStudentAcademic(CreateOrUpdateStudentAcademicRequest)
      returns (StudentResponse) {}
  rpc GetStudentAcademic(GetStudentAcademicRequest) returns (StudentResponse) {}
}

//----------------------------------------------------------------------------//
// Request & Response Messages
//----------------------------------------------------------------------------//
message CreateStudentRequest {
  int64 organizationId = 1;
  int64 agencyId = 2;  // nullable
  string firstName = 3;
  string lastName = 4;
  string nameInNative = 5;
  string email = 6;
  string phone = 7;
  string guardianPhone = 8;
  string dateOfBirth = 9;  // YYYY-MM-DD
  string gender = 10;
  string fatherName = 11;
  string motherName = 12;
  string nid = 13;
  string passport = 14;

  Address presentAddress = 15;
  Address permanentAddress = 16;
  MaritalStatus maritalStatus = 17;
  Sponsor sponsor = 18;
  EmergencyContact emergencyContact = 19;

  repeated string preferredSubject = 20;
  repeated string preferredCountry = 21;
  repeated SocialLink socialLinks = 22;

  string reference = 23;
  string note = 24;
  string studentId = 25;
}

message CreateStudentAfterRegistrationRequest {
  // The user’s internal ID (as a string)
  string user_id = 1;

  // Basic profile fields
  string name = 2;
  string email = 3;
  string phone = 4;
  string nationality = 5;

  // Context from auth flow
  string organization_name = 6;
  string role_name = 7;
  string department_name = 8;
}

message CreateOrUpdateStudentAcademicRequest {
  string student_id = 1;
  repeated StudentAcademicRecord academic_records = 2;
  repeated StudentProficiencyRecord proficiency_records = 3;
  repeated StudentPublication publications = 4;
  repeated StudentSocialLink socialLinks = 5;
  repeated StudentOtherActivity otherActivities = 6;
}

message GetStudentAcademicRequest {
  string student_id = 1;
}

message StudentAcademicResponse {
  StudentAcademicRecord academic_record = 1;
  StudentProficiencyRecord proficiency_record = 2;
  StudentPublication publication = 3;
  StudentSocialLink social_link = 4;
  StudentOtherActivity other_activity = 5;
}

//----------------------------------------------------------------------------//
// Domain Messages
//----------------------------------------------------------------------------//
message Student {
  int64 userId = 1;
  string studentId = 2;
  string status = 3;
  int64 organizationId = 4;
  string agencyId = 5;
  string metadata = 6;
  StudentPersonalInfo personalInfo = 7;
  repeated StudentAcademicRecord academic_records = 8;
  repeated StudentProficiencyRecord proficiency_records = 9;
  repeated StudentPublication publications = 10;
  repeated StudentSocialLink socialLinks = 11;
  repeated StudentOtherActivity otherActivities = 12;
  repeated Enrollment enrollments = 13;
  repeated StudentAcademicBackground academicBackgrounds = 14;
  repeated Grade grades = 15;
  repeated EmergencyContact emergencyContacts = 16;
}

message StudentPersonalInfo {
  int64 studentId = 1;
  string firstName = 2;
  string lastName = 3;
  string nameInNative = 4;
  string email = 5;
  string phone = 6;
  string guardianPhone = 7;
  string dateOfBirth = 8;
  string gender = 9;
  string fatherName = 10;
  string motherName = 11;
  string nid = 12;
  string passport = 13;

  Address presentAddress = 14;
  Address permanentAddress = 15;
  MaritalStatus maritalStatus = 16;
  Sponsor sponsor = 17;
  EmergencyContact emergencyContact = 18;

  repeated string preferredSubject = 19;
  repeated string preferredCountry = 20;
  repeated SocialLink socialLinks = 21;

  string reference = 22;
  string note = 23;

  // metadata
  string id = 24;
  string createdAt = 25;
  string updatedAt = 26;
}

//----------------------------------------------------------------------------//
// Nested Types
//----------------------------------------------------------------------------//
message Address {
  string address = 1;
  string country = 2;
  string state = 3;
  string city = 4;
  string postalCode = 5;
}

message MaritalStatus {
  string status = 1;
  string spouseName = 2;
  string spousePhone = 3;
  string spousePassport = 4;
}

message Sponsor {
  string name = 1;
  string relation = 2;
  string phone = 3;
  string email = 4;
}

message EmergencyContact {
  string lastName = 1;
  string middleName = 2;
  string firstName = 3;
  string phoneHome = 4;
  string phoneMobile = 5;
  string relation = 6;
}

message SocialLink {
  string platform = 1;
  string url = 2;
}

message GetStudentRequest {
  string id = 1;
}

message StudentResponse {
  Student student = 1;
}

message ListStudentsRequest {
  int32 page = 1;
  int32 limit = 2;
  string filter = 3;
  string orderBy = 4;
}

message ListStudentsResponse {
  repeated Student students = 1;
  int32 total = 2;
}

//----------------------------------------------------------------------------//
// Missing Proto Messages
//----------------------------------------------------------------------------//

message StudentPublication {
  int64 studentId = 1;
  string subject = 2;
  string journal = 3;
  string publicationDate = 4;
  string link = 5;
  string title = 6;
  string abstract = 7;
  repeated string authors = 8;
  string publicationType = 9;
  string doi = 10;
  string isbn = 11;
  string issn = 12;
  string volume = 13;
  string issue = 14;
  string pages = 15;
  string publisher = 16;
  string conferenceLocation = 17;
  string conferenceDate = 18;
  repeated string keywords = 19;
  double impactFactor = 20;
  int32 citationCount = 21;
  string status = 22;
  bool isPeerReviewed = 23;
  bool isOpenAccess = 24;
  repeated string documents = 25;
  string notes = 26;
  string metadata = 27;

  // metadata
  string id = 28;
  string createdAt = 29;
  string updatedAt = 30;
}

message StudentSocialLink {
  int64 studentId = 1;
  string platform = 2;
  string url = 3;
  string username = 4;
  string displayName = 5;
  bool isPublic = 6;
  bool isActive = 7;
  bool isPrimary = 8;
  bool isProfessional = 9;
  string description = 10;
  int32 followerCount = 11;
  int32 followingCount = 12;
  string lastVerified = 13;
  bool isVerified = 14;
  string notes = 15;
  string metadata = 16;

  // metadata
  string id = 17;
  string createdAt = 18;
  string updatedAt = 19;
}

message StudentOtherActivity {
  int64 studentId = 1;
  string subject = 2;
  string certificationLink = 3;
  string startDate = 4;
  string endDate = 5;
  string title = 6;
  string description = 7;
  string activityType = 8;
  string organization = 9;
  string instructor = 10;
  string location = 11;
  int32 durationHours = 12;
  string certificateNumber = 13;
  string grade = 14;
  double score = 15;
  bool isCertified = 16;
  bool isOngoing = 17;
  string certificationExpiryDate = 18;
  repeated string skills = 19;
  repeated string technologies = 20;
  double cost = 21;
  string currency = 22;
  bool isSponsored = 23;
  string sponsor = 24;
  repeated string documents = 25;
  string notes = 26;
  string metadata = 27;

  // metadata
  string id = 28;
  string createdAt = 29;
  string updatedAt = 30;
}

message StudentAcademicRecord {
  int64 studentId = 1;
  bool foreignDegree = 2;
  string nameOfExam = 3;
  string institute = 4;
  string subject = 5;
  string board = 6;
  string grade = 7;
  string passingYear = 8;
  double cgpa = 9;
  double percentage = 10;
  string division = 11;
  string rollNumber = 12;
  string registrationNumber = 13;
  string examDate = 14;
  string certificateNumber = 15;
  string notes = 16;
  repeated string documents = 17;
  string metadata = 18;

  // metadata
  string id = 19;
  string createdAt = 20;
  string updatedAt = 21;
}

message StudentProficiencyRecord {
  int64 studentId = 1;
  string nameOfExam = 2;
  ProficiencyScore score = 3;
  string examDate = 4;
  string expiryDate = 5;
  string note = 6;
  string testCenter = 7;
  string candidateNumber = 8;
  string certificateNumber = 9;
  string testType = 10;
  string band = 11;
  string level = 12;
  repeated string documents = 13;
  string metadata = 14;

  // metadata
  string id = 15;
  string createdAt = 16;
  string updatedAt = 17;
}

message ProficiencyScore {
  double overall = 1;
  double R = 2;  // Reading
  double L = 3;  // Listening
  double W = 4;  // Writing
  double S = 5;  // Speaking
}

message Enrollment {
  int64 studentId = 1;
  string course_id = 2;
  string course_name = 3;
  string course_code = 4;
  int32 credits = 5;
  string semester = 6;
  int32 academic_year = 7;
  string status = 8;
  string enrollment_date = 9;
  string completion_date = 10;
  string drop_date = 11;
  string instructor_id = 12;
  string instructor_name = 13;
  EnrollmentSchedule schedule = 14;
  string metadata = 15;

  // metadata
  string id = 16;
  string createdAt = 17;
  string updatedAt = 18;
}

message EnrollmentSchedule {
  repeated string days = 1;
  string start_time = 2;
  string end_time = 3;
  string location = 4;
}

message StudentAcademicBackground {
  int64 studentId = 1;
  repeated AcademicRecord academic_records = 2;
  repeated ProficiencyRecord proficiencyRecords = 3;
  repeated PublicationRecord publicationRecords = 4;
  OtherActivity otherActivities = 5;

  // metadata
  string id = 6;
  string createdAt = 7;
  string updatedAt = 8;
}

message AcademicRecord {
  bool foreign_degree = 1;
  string name_of_exam = 2;
  string institute = 3;
  string subject = 4;
  string board = 5;
  string grade = 6;
  string passing_year = 7;
}

message ProficiencyRecord {
  string name_of_exam = 1;
  ProficiencyScore score = 2;
  string exam_date = 3;
  string expiry_date = 4;
  string note = 5;
}

message PublicationRecord {
  string subject = 1;
  string journal = 2;
  string publication_date = 3;
  string link = 4;
}

message OtherActivity {
  string subject = 1;
  string certification_link = 2;
  string start_date = 3;
  string end_date = 4;
}

message Grade {
  int64 studentId = 1;
  int64 enrollment_id = 2;
  string course_id = 3;
  string course_name = 4;
  string course_code = 5;
  string letter_grade = 6;
  double grade_points = 7;
  double percentage = 8;
  int32 credits = 9;
  string semester = 10;
  int32 academic_year = 11;
  string grade_type = 12;
  string grade_date = 13;
  string instructor_id = 14;
  string instructor_name = 15;
  string comments = 16;
  GradeBreakdown breakdown = 17;
  string metadata = 18;

  // metadata
  string id = 19;
  string createdAt = 20;
  string updatedAt = 21;
}

message GradeBreakdown {
  double assignments = 1;
  double quizzes = 2;
  double midterm = 3;
  double final = 4;
  double participation = 5;
  double projects = 6;
}

message ContactAddress {
  string street = 1;
  string city = 2;
  string state = 3;
  string postal_code = 4;
  string country = 5;
}


