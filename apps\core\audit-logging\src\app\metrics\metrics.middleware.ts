import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { MetricsService } from '@apply-goal-backend/monitoring';

@Injectable()
export class MetricsMiddleware implements NestMiddleware {
  private requestCounter: any;
  private requestDuration: any;
  private requestSize: any;
  private responseSize: any;
  private activeRequests: any;

  constructor(private metricsService: MetricsService) {
    this.requestCounter = this.metricsService.createCounter(
      'audit_http_requests_total',
      'Total number of HTTP requests',
      ['method', 'path', 'status']
    );

    this.requestDuration = this.metricsService.createHistogram(
      'audit_http_request_duration_seconds',
      'HTTP request duration in seconds',
      ['method', 'path'],
      [0.01, 0.05, 0.1, 0.5, 1, 2, 5]
    );

    this.requestSize = this.metricsService.createHistogram(
      'audit_http_request_size_bytes',
      'HTTP request size in bytes',
      ['method', 'path']
    );

    this.responseSize = this.metricsService.createHistogram(
      'audit_http_response_size_bytes',
      'HTTP response size in bytes',
      ['method', 'path']
    );

    this.activeRequests = this.metricsService.createGauge(
      'audit_http_active_requests',
      'Number of active HTTP requests',
      ['method']
    );
  }

  use(req: Request, res: Response, next: NextFunction) {
    const start = Date.now();
    const { method, path } = req;

    // Track request size
    const requestSize = req.headers['content-length'] ? 
      parseInt(req.headers['content-length'], 10) : 0;
    this.requestSize.observe({ method, path }, requestSize);

    // Increment active requests
    this.activeRequests.inc({ method });

    // Capture original end
    const originalEnd = res.end;
    let responseBody = Buffer.from('');

    // Override end
    res.end = (...args: any[]) => {
      const responseSize = res.hasHeader('content-length') ?
        parseInt(res.getHeader('content-length') as string, 10) : 0;
      
      const duration = (Date.now() - start) / 1000;
      const status = res.statusCode.toString();

      // Record metrics
      this.requestCounter.inc({ method, path, status });
      this.requestDuration.observe({ method, path }, duration);
      this.responseSize.observe({ method, path }, responseSize);
      
      // Decrement active requests
      this.activeRequests.dec({ method });

      // Call original end
      return originalEnd.apply(res, args);
    };

    next();
  }
}
