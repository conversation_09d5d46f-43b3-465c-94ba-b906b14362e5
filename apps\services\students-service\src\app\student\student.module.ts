import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { SequelizeModule } from '@nestjs/sequelize';
import { join } from 'path';
import { StudentController } from './student.controller';
import { StudentService } from './student.service';
import { StudentDocumentsModule } from './student-documents.module';
import { StudentDocumentsGrpcController } from './controllers/student-documents-grpc.controller';
import { Student } from './models/student.model';
import { Enrollment } from './models/enrollment.model';
import { Grade } from './models/grade.model';
import { EmergencyContact } from './models/emergency-contact.model';
import { StudentAcademicBackground } from './models/student-academic.model';
import { StudentPersonalInfo } from './models/student-personal-info.model';
import { StudentAcademicRecord } from './models/student-academic-record.model';
import { StudentProficiencyRecord } from './models/student-proficiency-record.model';
import { StudentPublication } from './models/student-publication.model';
import { StudentSocialLink } from './models/student-social-link.model';
import { StudentOtherActivity } from './models/student-other-activity.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Student,
      Enrollment,
      Grade,
      EmergencyContact,
      StudentAcademicBackground,
      StudentPersonalInfo,
      StudentAcademicRecord,
      StudentProficiencyRecord,
      StudentPublication,
      StudentSocialLink,
      StudentOtherActivity,
    ]),
    ClientsModule.register([
      {
        name: 'AUTH_SERVICE',
        transport: Transport.GRPC,
        options: {
          package: 'auth',
          protoPath: join(
            process.cwd(),
            'libs/shared/dto/src/lib/auth/auth.proto'
          ),
          url: process.env.AUTH_SERVICE_URL || 'auth-service:50052'
        }
      }
    ]),
    StudentDocumentsModule
  ],
  controllers: [StudentController, StudentDocumentsGrpcController],
  providers: [StudentService],
  exports: [StudentService]
})
export class StudentModule {}
