import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'student_other_activities',
  indexes: [
    { fields: ['studentId'] },
    { fields: ['subject'] },
    { fields: ['activityType'] },
    { fields: ['startDate'] },
    { fields: ['endDate'] }
  ]
})
export class StudentOtherActivity extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.STRING,
    allowNull: false,
    references: {
      model: 'students',
      key: 'studentId'
    }
  })
  studentId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  subject!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  certificationLink?: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: false
  })
  startDate!: Date;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true
  })
  endDate?: Date;

  // Additional fields for comprehensive activity tracking
  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  title!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  description?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'certification'
  })
  activityType!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  organization?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  instructor?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  location?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  durationHours?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  certificateNumber?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  grade?: string;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true
  })
  score?: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isCertified!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isOngoing!: boolean;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true
  })
  certificationExpiryDate?: Date;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  skills?: string[]; // Skills gained from this activity

  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  technologies?: string[]; // Technologies learned

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true
  })
  cost?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  currency?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isSponsored!: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  sponsor?: string;

  // Document attachments (JSON array of file paths/URLs)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  documents?: string[];

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  notes?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student, { foreignKey: 'studentId', targetKey: 'studentId' })
  student!: Student;

  // Virtual properties
  get isCompleted(): boolean {
    return !this.isOngoing && !!this.endDate;
  }

  get duration(): number {
    if (!this.endDate) return 0;
    const start = new Date(this.startDate);
    const end = new Date(this.endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Duration in days
  }

  get durationInMonths(): number {
    return Math.ceil(this.duration / 30);
  }

  get isRecent(): boolean {
    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
    return new Date(this.startDate) > twoYearsAgo;
  }

  get isCertificationExpired(): boolean {
    if (!this.certificationExpiryDate) return false;
    return new Date() > new Date(this.certificationExpiryDate);
  }

  get isCertificationValid(): boolean {
    return this.isCertified && !this.isCertificationExpired;
  }

  get hasSkills(): boolean {
    return this.skills && this.skills.length > 0;
  }

  get hasTechnologies(): boolean {
    return this.technologies && this.technologies.length > 0;
  }

  get hasDocuments(): boolean {
    return this.documents && this.documents.length > 0;
  }

  get isProfessionalDevelopment(): boolean {
    const professionalTypes = [
      'certification',
      'course',
      'training',
      'workshop',
      'seminar',
      'conference'
    ];
    return professionalTypes.includes(this.activityType);
  }

  get isAcademicActivity(): boolean {
    const academicTypes = ['research', 'scholarship', 'award', 'competition'];
    return academicTypes.includes(this.activityType);
  }

  get isWorkExperience(): boolean {
    const workTypes = ['internship', 'project'];
    return workTypes.includes(this.activityType);
  }

  get activityCategory(): string {
    if (this.isProfessionalDevelopment) return 'professional_development';
    if (this.isAcademicActivity) return 'academic';
    if (this.isWorkExperience) return 'work_experience';
    if (this.activityType === 'volunteer') return 'volunteer';
    return 'other';
  }
}
