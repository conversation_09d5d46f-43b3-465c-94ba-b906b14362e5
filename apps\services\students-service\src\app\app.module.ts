import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics.controller';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { MetricsMiddleware } from './middleware/metrics.middleware';
import { StudentModule } from './student/student.module';
import { DatabaseModule } from './migration/database.module';
import { MigrationModule } from './migration/migration.module';

@Module({
  imports: [
    DatabaseModule,
    MigrationModule,
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'students-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5008', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
        },
      },
      tracing: {
        serviceName: 'students-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces',
      },
    }),
    StudentModule,
  ],
  controllers: [AppController, MetricsController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(MetricsMiddleware)
      .exclude('/metrics') // Exclude metrics endpoint to avoid recursive tracking
      .forRoutes('*');
  }
}
