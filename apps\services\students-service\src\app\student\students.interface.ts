// student.proto → TypeScript interfaces

export interface CreateStudentRequest {
  studentId?: string | null;
  organizationId: number;
  agencyId?: number | null;
  firstName: string;
  lastName: string;
  nameInNative: string;
  email: string;
  phone: string;
  guardianPhone: string;
  dateOfBirth: string; // YYYY-MM-DD
  gender: string;
  fatherName: string;
  motherName: string;
  nid: string;
  passport: string;

  presentAddress: Address;
  permanentAddress: Address;
  maritalStatus: MaritalStatus;
  sponsor: Sponsor;
  emergencyContact: EmergencyContact;

  preferredSubject: string[];
  preferredCountry: string[];
  socialLinks: SocialLink[];

  reference: string;
  note: string;
}

export interface StudentResponse {
  student: Student;
}

export interface CreateStudentAfterRegistrationRequest {
  userId: string;
  name: string;
  email: string;
  phone: string;
  nationality: string;

  organization_name: string;
  role_name: string;
  department_name: string;
}

export interface Student {
  // original CreateStudentRequest fields
  organizationId: number;
  agencyId?: number | null;
  firstName: string;
  lastName: string;
  nameInNative: string;
  email: string;
  phone: string;
  guardianPhone: string;
  dateOfBirth: string;
  gender: string;
  fatherName: string;
  motherName: string;
  nid: string;
  passport: string;

  presentAddress: Address;
  permanentAddress: Address;
  maritalStatus: MaritalStatus;
  sponsor: Sponsor;
  emergencyContact: EmergencyContact;

  preferredSubject: string[];
  preferredCountry: string[];
  socialLinks: SocialLink[];

  reference: string;
  note: string;

  // metadata
  id: string;
  createdAt: string; // ISO8601
  updatedAt: string; // ISO8601
}

export interface Address {
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
}

export interface MaritalStatus {
  status: string;
  spouseName: string;
  spousePhone: string;
  spousePassport: string;
}

export interface Sponsor {
  name: string;
  relation: string;
  phone: string;
  email: string;
}

export interface EmergencyContact {
  lastName: string;
  middleName: string;
  firstName: string;
  phoneHome: string;
  phoneMobile: string;
  relation: string;
}

export interface SocialLink {
  platform: string;
  url: string;
}
