import { Module } from '@nestjs/common';
import { RouterModule } from '@nestjs/core';
import { HealthController, ServiceHealthController } from '@apply-goal-backend/health-check';

@Module({
  controllers: [],
  providers: [
    {
      provide: 'SERVICE_HEALTH_CONTROLLER',
      useFactory: () => new ServiceHealthController('audit-logging'),
    },
    {
      provide: 'HEALTH_CONTROLLER',
      useClass: HealthController,
    },
  ],
  exports: [],
})
export class HealthModule {}
