import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  OnModuleInit,
  Inject
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, WhereOptions } from 'sequelize';
import { ClientGrpc } from '@nestjs/microservices';
import { firstValueFrom, Observable } from 'rxjs';
import { Student } from './models/student.model';
import { Enrollment } from './models/enrollment.model';
import { Grade } from './models/grade.model';
import { EmergencyContact } from './models/emergency-contact.model';
import { StudentAcademicBackground } from './models/student-academic.model';
import { StudentPersonalInfo } from './models/student-personal-info.model';
import { StudentAcademicRecord } from './models/student-academic-record.model';
import { StudentProficiencyRecord } from './models/student-proficiency-record.model';
import { StudentPublication } from './models/student-publication.model';
import { StudentSocialLink } from './models/student-social-link.model';
import { StudentOtherActivity } from './models/student-other-activity.model';
import { CreateStudentRequest } from './students.interface';

interface AuthService {
  createStudent(data: any): Observable<any>;
}

@Injectable()
export class StudentService implements OnModuleInit {
  private readonly logger = new Logger(StudentService.name);
  private authService: AuthService;

  constructor(
    @InjectModel(Student)
    private readonly studentModel: typeof Student,
    @InjectModel(Enrollment)
    private readonly enrollmentModel: typeof Enrollment,
    @InjectModel(Grade)
    private readonly gradeModel: typeof Grade,
    @InjectModel(EmergencyContact)
    private readonly emergencyContactModel: typeof EmergencyContact,
    @InjectModel(StudentAcademicBackground)
    private readonly studentAcademicModel: typeof StudentAcademicBackground,
    @InjectModel(StudentPersonalInfo)
    private readonly studentPersonalInfoModel: typeof StudentPersonalInfo,
    @InjectModel(StudentAcademicRecord)
    private readonly studentAcademicRecordModel: typeof StudentAcademicRecord,
    @InjectModel(StudentProficiencyRecord)
    private readonly studentProficiencyRecordModel: typeof StudentProficiencyRecord,
    @InjectModel(StudentPublication)
    private readonly studentPublicationModel: typeof StudentPublication,
    @InjectModel(StudentSocialLink)
    private readonly studentSocialLinkModel: typeof StudentSocialLink,
    @InjectModel(StudentOtherActivity)
    private readonly studentOtherActivityModel: typeof StudentOtherActivity,
    @Inject('AUTH_SERVICE')
    private readonly authClient: ClientGrpc
  ) {}

  onModuleInit() {
    this.authService = this.authClient.getService<AuthService>('AuthService');
  }

  // Student CRUD Operations
  async createStudent(studentData: CreateStudentRequest): Promise<Student> {
    try {
      // Check if student with studentId already exists
      const studentInfo = {
        firstName: studentData.firstName || '',
        lastName: studentData.lastName || '',
        nameInNative: studentData.nameInNative || '',
        email: studentData.email,
        phone: studentData.phone || '',
        guardianPhone: studentData.guardianPhone || '',
        dateOfBirth: studentData.dateOfBirth || null,
        gender: studentData.gender,
        fatherName: studentData.fatherName || '',
        motherName: studentData.motherName || '',
        nid: studentData.nid || '',
        passport: studentData.passport || '',
        presentAddress: studentData.presentAddress || null,
        permanentAddress: studentData.permanentAddress || null,
        maritalStatus: studentData.maritalStatus || null,
        sponsor: studentData.sponsor || null,
        emergencyContact: studentData.emergencyContact || null,
        preferredSubject: studentData.preferredSubject || [],
        preferredCountry: studentData.preferredCountry || [],
        socialLinks: studentData.socialLinks || [],
        reference: studentData.reference || '',
        note: studentData.note || ''
      };

      const existingStudent = await this.studentPersonalInfoModel.findOne({
        where: { email: studentData.email }
      });

      if (existingStudent) {
        this.logger.log(`Existing student: ${existingStudent}`);
        // update student
        if (existingStudent && studentData.studentId) {
          // First, verify that the studentId exists in the students table
          const studentExists = await this.studentModel.findOne({
            where: { studentId: studentData.studentId }
          });

          if (!studentExists) {
            throw new NotFoundException(
              `Student with ID ${studentData.studentId} not found`
            );
          }

          const updatedStudentInfo = {
            ...studentInfo,
            studentId: studentExists.id
          };
          this.logger.log(
            `Updating student: ${JSON.stringify(existingStudent, null, 2)}`
          );
          await existingStudent.update(updatedStudentInfo);

          this.logger.log(
            `Student updated successfully: ${existingStudent.id}`
          );

          // fetch full student information with all related data
          const fullStudent = await this.studentModel.findOne({
            where: { studentId: existingStudent.studentId },
            include: [
              { 
                model: this.studentPersonalInfoModel,
                as: 'personalInfo',
                required: false
              },
              { 
                model: this.enrollmentModel,
                as: 'enrollments',
                required: false
              },
              { 
                model: this.gradeModel,
                as: 'grades',
                required: false
              },
              { 
                model: this.emergencyContactModel,
                as: 'emergency_contacts',
                required: false
              },
              { 
                model: this.studentAcademicModel,
                as: 'academicBackgrounds',
                required: false
              }
            ]
          });

          this.logger.log(
            `Full student data: ${JSON.stringify(fullStudent, null, 2)}`
          );

          if (!fullStudent) {
            throw new NotFoundException('Student not found after update');
          }

          this.logger.log(`Updated student with full data: ${fullStudent.id}`);
          return fullStudent;
        } else {
          throw new ConflictException('User exists with email');
        }
      } else {
        // create student
        // Generate unique student ID
        const studentId = await this.generateStudentId();

        this.logger.log(`Creating student with studentId: ${studentId}`);

        const savedStudent = await this.studentModel.create({
          ...studentData,
          organizationId: studentData.organizationId
            ? Number(studentData.organizationId)
            : 1,
          agency_id: studentData.agencyId
            ? studentData.agencyId.toString()
            : null,
          studentId: studentId,
          enrollment_date: new Date()
        });

        this.logger.log(
          `Student created successfully: ${JSON.stringify(
            savedStudent,
            null,
            2
          )}`
        );

        if (!savedStudent || !savedStudent.id) {
          throw new Error('Failed to create student - no ID returned');
        }

        // Create personal info if provided
        if (
          studentData.email ||
          studentData.firstName ||
          studentData.lastName
        ) {
          this.logger.log(
            `Creating personal info with studentId: ${savedStudent.id}`
          );
          await this.studentPersonalInfoModel.create({
            studentId: savedStudent.id,
            ...studentInfo
          });
          this.logger.log(`Personal info created successfully`);
        }

        const userData = {
          name: studentData.firstName + ' ' + studentData.lastName,
          email: studentData.email,
          password: 'TempPassword123!', // Temporary password
          phone: studentData.phone,
          nationality: studentData.permanentAddress?.country || '',
          organizationId: studentData.organizationId
            ? Number(studentData.organizationId)
            : 1,
          roleName: 'Student',
          departmentName: 'Students',
          ipAddress: '127.0.0.1',
          userAgent: 'Student Registration System'
        };
        const studentUser = await firstValueFrom(
          this.authService.createStudent(userData)
        );

        this.logger.log(
          `User created successfully`,
          JSON.stringify(studentUser, null, 2)
        );
        // fetch full student information with all related data
        const fullStudent = await this.studentModel.findOne({
          where: { studentId: savedStudent.studentId },
          include: [
            { 
              model: this.studentPersonalInfoModel,
              as: 'personalInfo',
              required: false
            },
            { 
              model: this.enrollmentModel,
              as: 'enrollments',
              required: false
            },
            { 
              model: this.gradeModel,
              as: 'grades',
              required: false
            },
            { 
              model: this.emergencyContactModel,
              as: 'emergency_contacts',
              required: false
            },
            { 
              model: this.studentAcademicModel,
              as: 'academicBackgrounds',
              required: false
            }
          ]
        });

        if (!fullStudent) {
          throw new Error('Failed to fetch created student with full data');
        }

        this.logger.log(`Created student with full data: ${fullStudent.id}`);
        return fullStudent;
      }
    } catch (error) {
      this.logger.error('Error creating student:', error);
      throw error;
    }
  }

  async getStudent(id: string): Promise<Student> {
    try {
      const student = await this.studentModel.findOne({
        where: { studentId: id },
        include: [
          { 
            model: this.studentPersonalInfoModel,
            as: 'personalInfo',
            required: false
          },
          { 
            model: this.enrollmentModel,
            as: 'enrollments',
            required: false
          },
          { 
            model: this.gradeModel,
            as: 'grades',
            required: false
          },
          { 
            model: this.emergencyContactModel,
            as: 'emergency_contacts',
            required: false
          },
          { 
            model: this.studentAcademicModel,
            as: 'academicBackgrounds',
            required: false
          }
        ]
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      this.logger.debug(`Student data: ${JSON.stringify(student, null, 2)}`);

      return student;
    } catch (error) {
      this.logger.error('Error getting student:', error);
      throw error;
    }
  }

  async updateStudent(id: string, updateData: any): Promise<Student> {
    try {
      const student = await this.getStudent(id);

      // Update student data
      await student.update(updateData);

      // Update emergency contacts if provided
      if (updateData.emergency_contacts) {
        // Remove existing emergency contacts
        await this.emergencyContactModel.destroy({
          where: { studentId: id }
        });

        // Create new emergency contacts
        const student = await this.studentModel.findOne({
          where: { studentId: id }
        });
        if (!student) {
          throw new NotFoundException('Student not found');
        }
        
        const emergencyContacts = updateData.emergency_contacts.map(
          (contact: any) => ({
            ...contact,
            studentId: student.id
          })
        );
        await this.emergencyContactModel.bulkCreate(emergencyContacts);
      }

      this.logger.log(`Updated student: ${id}`);
      return student;
    } catch (error) {
      this.logger.error('Error updating student:', error);
      throw error;
    }
  }

  async deleteStudent(id: string): Promise<void> {
    try {
      const student = await this.getStudent(id);
      await student.destroy();
      this.logger.log(`Deleted student: ${id}`);
    } catch (error) {
      this.logger.error('Error deleting student:', error);
      throw error;
    }
  }

  async listStudents(
    filters: any
  ): Promise<{ students: Student[]; total: number; page_token?: string }> {
    try {
      const {
        page_size = 10,
        page_token = '',
        filter = '',
        order_by = 'createdAt desc'
      } = filters;

      const whereCondition: WhereOptions = {};

      // Apply filters
      if (filter) {
        (whereCondition as any)[Op.or] = [
          {
            studentId: { [Op.iLike]: `%${filter}%` },
            organizationId: filters.organizationId
              ? Number(filters.organizationId)
              : undefined
          }
        ];
      }

      // Apply ordering
      const [orderField, orderDirection] = order_by.split(' ');
      const order: any = [[orderField, orderDirection.toUpperCase()]];

      // Apply pagination
      const offset = page_token ? parseInt(page_token, 10) : 0;

      const { rows: students, count: total } =
        await this.studentModel.findAndCountAll({
          where: whereCondition,
          order,
          limit: page_size,
          offset,
          include: [
            { 
              model: this.studentPersonalInfoModel,
              as: 'personalInfo',
              required: false
            },
            { 
              model: this.enrollmentModel,
              as: 'enrollments',
              required: false
            },
            { 
              model: this.gradeModel,
              as: 'grades',
              required: false
            },
            { 
              model: this.emergencyContactModel,
              as: 'emergency_contacts',
              required: false
            },
            { 
              model: this.studentAcademicModel,
              as: 'academicBackgrounds',
              required: false
            },
            { 
              model: this.studentAcademicRecordModel,
              as: 'academicRecords',
              required: false
            },
            { 
              model: this.studentProficiencyRecordModel,
              as: 'proficiencyRecords',
              required: false
            },
            { 
              model: this.studentPublicationModel,
              as: 'publications',
              required: false
            },
            { 
              model: this.studentSocialLinkModel,
              as: 'socialLinks',
              required: false
            },
            { 
              model: this.studentOtherActivityModel,
              as: 'otherActivities',
              required: false
            }
          ]
        });

      const nextPageToken =
        offset + page_size < total
          ? (offset + page_size).toString()
          : undefined;

      return {
        students,
        total,
        page_token: nextPageToken
      };
    } catch (error) {
      this.logger.error('Error listing students:', error);
      throw error;
    }
  }

  // Academic Operations
  async enrollInCourse(
    studentId: string,
    courseId: string,
    semester: string,
    courseData?: any
  ): Promise<Enrollment> {
    try {
      // Check if student exists
      await this.getStudent(studentId);

      // Check if already enrolled in this course for this semester
      const existingEnrollment = await this.enrollmentModel.findOne({
        where: { studentId: studentId, course_id: courseId, semester }
      });

      if (existingEnrollment) {
        throw new ConflictException(
          'Student is already enrolled in this course for this semester'
        );
      }

      // Get the student record to get the ID
      const student = await this.studentModel.findOne({
        where: { studentId: studentId }
      });
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const savedEnrollment = await this.enrollmentModel.create({
        studentId: student.id,
        course_id: courseId,
        semester,
        enrollment_date: new Date(),
        academic_year: new Date().getFullYear(),
        ...courseData
      });
      this.logger.log(`Student ${studentId} enrolled in course ${courseId}`);
      return savedEnrollment;
    } catch (error) {
      this.logger.error('Error enrolling in course:', error);
      throw error;
    }
  }

  async dropCourse(
    studentId: string,
    courseId: string,
    semester: string
  ): Promise<void> {
    try {
      const enrollment = await this.enrollmentModel.findOne({
        where: { studentId: studentId, course_id: courseId, semester }
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      await enrollment.update({
        status: 'dropped',
        drop_date: new Date()
      });

      this.logger.log(`Student ${studentId} dropped course ${courseId}`);
    } catch (error) {
      this.logger.error('Error dropping course:', error);
      throw error;
    }
  }

  async getEnrollments(
    studentId: string,
    semester?: string
  ): Promise<Enrollment[]> {
    try {
      const whereCondition: any = { studentId: studentId };
      if (semester) {
        whereCondition.semester = semester;
      }

      const enrollments = await this.enrollmentModel.findAll({
        where: whereCondition,
        include: [{ model: this.gradeModel }],
        order: [['createdAt', 'DESC']]
      });

      return enrollments;
    } catch (error) {
      this.logger.error('Error getting enrollments:', error);
      throw error;
    }
  }

  async updateGrades(
    studentId: string,
    courseId: string,
    gradeData: any
  ): Promise<Grade> {
    try {
      // Find the enrollment
      const enrollment = await this.enrollmentModel.findOne({
        where: { studentId: studentId, course_id: courseId }
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      // Check if grade already exists
      let grade = await this.gradeModel.findOne({
        where: { studentId: studentId, enrollment_id: enrollment.id }
      });

      if (grade) {
        // Update existing grade
        await grade.update(gradeData);
      } else {
        // Get the student record to get the ID
        const student = await this.studentModel.findOne({
          where: { studentId: studentId }
        });
        if (!student) {
          throw new NotFoundException('Student not found');
        }

        // Create new grade
        grade = await this.gradeModel.create({
          studentId: student.id,
          enrollment_id: enrollment.id,
          course_id: courseId,
          grade_date: new Date(),
          academic_year: enrollment.academic_year,
          semester: enrollment.semester,
          credits: enrollment.credits,
          course_name: enrollment.course_name,
          course_code: enrollment.course_code,
          ...gradeData
        });
      }

      // Update student GPA
      await this.updateStudentGPA(studentId);

      this.logger.log(
        `Updated grade for student ${studentId} in course ${courseId}`
      );
      return grade;
    } catch (error) {
      this.logger.error('Error updating grades:', error);
      throw error;
    }
  }

  async getAcademicProgress(studentId: string): Promise<any> {
    try {
      const student = await this.getStudent(studentId);
      const enrollments = await this.getEnrollments(studentId);
      const grades = await this.gradeModel.findAll({
        where: { studentId: studentId },
        order: [
          ['academic_year', 'DESC'],
          ['semester', 'DESC']
        ]
      });

      // Calculate academic statistics
      const totalCreditsAttempted = grades.reduce(
        (sum, grade) => sum + grade.credits,
        0
      );
      const totalQualityPoints = grades.reduce(
        (sum, grade) => sum + grade.quality_points,
        0
      );
      const currentGPA =
        totalCreditsAttempted > 0
          ? totalQualityPoints / totalCreditsAttempted
          : 0;

      const completedCourses = grades.filter(
        (grade) => grade.is_passing
      ).length;
      const failedCourses = grades.filter((grade) => !grade.is_passing).length;

      return {
        studentId: studentId,
        student_name: student.full_name,
        current_gpa: Math.round(currentGPA * 100) / 100,
        total_credits_attempted: totalCreditsAttempted,
        total_credits_earned: grades
          .filter((g) => g.is_passing)
          .reduce((sum, g) => sum + g.credits, 0),
        completed_courses: completedCourses,
        failed_courses: failedCourses,
        current_enrollments: enrollments.filter((e) => e.is_current).length,
        academic_standing: this.getAcademicStanding(currentGPA),
        semester_breakdown: this.getSemesterBreakdown(grades)
      };
    } catch (error) {
      this.logger.error('Error getting academic progress:', error);
      throw error;
    }
  }

  async getTranscript(studentId: string): Promise<any> {
    try {
      const student = await this.getStudent(studentId);
      const grades = await this.gradeModel.findAll({
        where: { studentId: studentId },
        order: [
          ['academic_year', 'ASC'],
          ['semester', 'ASC']
        ]
      });

      const semesterGroups = this.groupGradesBySemester(grades);

      return {
        student_info: {
          id: student.id,
          studentId: student.studentId,
          name: student.full_name,
          email: student.personalInfo?.email || '',
          major: 'Not specified', // This field was moved to academic records
          minor: 'Not specified', // This field was moved to academic records
          // enrollment_date: student.enrollment_date,
          // graduation_date: student.graduation_date,
          status: student.status
        },
        academic_summary: {
          // cumulative_gpa: student.gpa,
          // total_credits: student.total_credits,
          // completed_credits: student.total_credits, // Using total_credits as completed_credits
          // academic_standing: this.getAcademicStanding(student.gpa || 0),
        },
        semester_records: semesterGroups,
        generated_at: new Date()
      };
    } catch (error) {
      this.logger.error('Error getting transcript:', error);
      throw error;
    }
  }

  // Helper methods
  private async generateStudentId(): Promise<string> {
    const year = new Date().getFullYear().toString().slice(-2);
    const randomNum = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `ST${year}${randomNum}`;
  }

  private async updateStudentGPA(studentId: string): Promise<void> {
    try {
      const grades = await this.gradeModel.findAll({
        where: { studentId: studentId }
      });

      const totalCreditsAttempted = grades.reduce(
        (sum, grade) => sum + grade.credits,
        0
      );
      const totalQualityPoints = grades.reduce(
        (sum, grade) => sum + grade.quality_points,
        0
      );

      const gpa =
        totalCreditsAttempted > 0
          ? totalQualityPoints / totalCreditsAttempted
          : 0;

      await this.studentModel.update(
        {
          gpa: Math.round(gpa * 100) / 100,
          total_credits: totalCreditsAttempted
        },
        { where: { studentId: studentId } }
      );
    } catch (error) {
      this.logger.error('Error updating student GPA:', error);
    }
  }

  private getAcademicStanding(gpa: number): string {
    if (gpa >= 3.5) return "Dean's List";
    if (gpa >= 3.0) return 'Good Standing';
    if (gpa >= 2.0) return 'Academic Warning';
    return 'Academic Probation';
  }

  private getSemesterBreakdown(grades: Grade[]): any[] {
    const semesterGroups = this.groupGradesBySemester(grades);
    return semesterGroups.map((group) => ({
      semester: group.semester,
      academic_year: group.academic_year,
      courses: group.grades.length,
      credits_attempted: group.grades.reduce((sum, g) => sum + g.credits, 0),
      credits_earned: group.grades
        .filter((g) => g.is_passing)
        .reduce((sum, g) => sum + g.credits, 0),
      semester_gpa:
        group.grades.length > 0
          ? Math.round(
              (group.grades.reduce((sum, g) => sum + g.quality_points, 0) /
                group.grades.reduce((sum, g) => sum + g.credits, 0)) *
                100
            ) / 100
          : 0
    }));
  }

  private groupGradesBySemester(grades: Grade[]): any[] {
    const groups: any = {};
    grades.forEach((grade) => {
      const key = `${grade.academic_year}-${grade.semester}`;
      if (!groups[key]) {
        groups[key] = {
          academic_year: grade.academic_year,
          semester: grade.semester,
          grades: []
        };
      }
      groups[key].grades.push(grade);
    });
    return Object.values(groups);
  }

  // Academic Background Operations
  async createOrUpdateStudentAcademic(
    academicData: any
  ): Promise<StudentAcademicBackground> {
    try {
      const student = await this.studentModel.findOne({
        where: { studentId: academicData.studentId }
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      let academicBackground = await this.studentAcademicModel.findOne({
        where: { studentId: academicData.studentId }
      });

      if (academicBackground) {
        // Update existing academic background
        await academicBackground.update(academicData);
      } else {
        // Get the student record to get the ID
        const student = await this.studentModel.findOne({
          where: { studentId: academicData.studentId }
        });
        if (!student) {
          throw new NotFoundException('Student not found');
        }

        // Create new academic background
        academicBackground = await this.studentAcademicModel.create({
          studentId: student.id,
          ...academicData
        });
      }

      this.logger.log(
        `Academic background ${
          academicBackground ? 'updated' : 'created'
        } for student ${academicData.studentId}`
      );
      return academicBackground;
    } catch (error) {
      this.logger.error('Error creating/updating academic background:', error);
      throw error;
    }
  }

  async getStudentAcademic(
    studentId: string
  ): Promise<StudentAcademicBackground> {
    try {
      const student = await this.studentModel.findOne({
        where: { studentId: studentId }
      });
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const academicBackground = await this.studentAcademicModel.findOne({
        where: { studentId: studentId }
      });

      if (!academicBackground) {
        throw new NotFoundException('Academic background not found');
      }

      return academicBackground;
    } catch (error) {
      this.logger.error('Error getting student academic background:', error);
      throw error;
    }
  }

  // Check if student exists by email
  async checkStudentExistsByEmail(email: string): Promise<Student | null> {
    try {
      const personalInfo = await this.studentPersonalInfoModel.findOne({
        where: { email: email }
      });

      if (!personalInfo) {
        return null;
      }

      const student = await this.studentModel.findOne({
        where: { studentId: personalInfo.studentId },
        include: [
          { 
            model: this.studentPersonalInfoModel,
            as: 'personalInfo',
            required: false
          },
          { 
            model: this.enrollmentModel,
            as: 'enrollments',
            required: false
          },
          { 
            model: this.gradeModel,
            as: 'grades',
            required: false
          },
          { 
            model: this.emergencyContactModel,
            as: 'emergency_contacts',
            required: false
          },
          { 
            model: this.studentAcademicModel,
            as: 'academicBackgrounds',
            required: false
          }
        ]
      });

      return student;
    } catch (error) {
      this.logger.error('Error checking student by email:', error);
      return null;
    }
  }

  // Get student by email
  async getStudentByEmail(email: string): Promise<Student | null> {
    try {
      const personalInfo = await this.studentPersonalInfoModel.findOne({
        where: { email: email }
      });

      if (!personalInfo) {
        return null;
      }

      const student = await this.studentModel.findOne({
        where: { studentId: personalInfo.studentId },
        include: [
          { 
            model: this.studentPersonalInfoModel,
            as: 'personalInfo',
            required: false
          },
          { 
            model: this.enrollmentModel,
            as: 'enrollments',
            required: false
          },
          { 
            model: this.gradeModel,
            as: 'grades',
            required: false
          },
          { 
            model: this.emergencyContactModel,
            as: 'emergency_contacts',
            required: false
          },
          { 
            model: this.studentAcademicModel,
            as: 'academicBackgrounds',
            required: false
          }
        ]
      });

      return student;
    } catch (error) {
      this.logger.error('Error getting student by email:', error);
      return null;
    }
  }
}
