import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  // Setup gRPC Microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'agency',
      protoPath: join(__dirname, '../../../../libs/shared/dto/src/lib/agency/agency.proto'),
      url: '0.0.0.0:50051',
    },
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  const port = process.env.PORT || 3000;

  // Start HTTP server
  await app.startAllMicroservices(); // <- start microservices (gRPC)
  await app.listen(port);             // <- start HTTP server

  Logger.log(`🚀 HTTP Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`🚀 gRPC server is running on: 0.0.0.0:50051`);
}

bootstrap();
