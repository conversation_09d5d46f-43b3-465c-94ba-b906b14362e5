# --------------------------------------------
# Dev Dockerfile for payment-service (NestJS + Nx)
# Supports hot reload and Traefik integration
# --------------------------------------------

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install curl (needed for healthcheck) and bash (optional for dev debugging)
RUN apk --no-cache add curl bash

# Install pnpm
RUN corepack enable && corepack prepare pnpm@8.15.1 --activate

# Set pnpm store directory
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

# Copy only essential config files first to leverage caching
COPY pnpm-lock.yaml ./
COPY package.json ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./

# Copy project configuration
COPY apps/core/payment-service/project.json ./apps/core/payment-service/
COPY libs/*/project.json ./libs/
COPY libs/shared/health-check/package.json ./libs/shared/health-check/

# Enable pnpm workspace mode
COPY pnpm-workspace.yaml ./

# Install dependencies with store cache
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    pnpm install --frozen-lockfile

# Copy service-specific and shared source code
COPY apps/core/payment-service ./apps/core/payment-service
COPY libs ./libs

# Build shared libraries first
RUN pnpm nx run-many --target=build --projects=utils,health-check,auth,common,config,database,decorators,dto,interfaces,logging --parallel=3

# Expose the port used by payment-service
EXPOSE 3003

# Define a healthcheck for Docker + Traefik
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3003/health || exit 1

# Run service in dev mode with live reloading
CMD ["pnpm", "nx", "serve", "payment-service", "--configuration=development", "--host=0.0.0.0"]

