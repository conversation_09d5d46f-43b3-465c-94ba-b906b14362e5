import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Table,
  Index,
  Default
} from 'sequelize-typescript';
import { Student } from './student.model';

@Table({
  tableName: 'student_academic_records',
  indexes: [
    { fields: ['studentId'] },
    { fields: ['nameOfExam'] },
    { fields: ['passingYear'] },
    { fields: ['institute'] }
  ]
})
export class StudentAcademicRecord extends BaseModel {
  @ForeignKey(() => Student)
  @Column({
    type: DataType.STRING,
    allowNull: false,
    references: {
      model: 'students',
      key: 'studentId'
    }
  })
  studentId!: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  foreignDegree!: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  nameOfExam!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  institute!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  subject!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  board?: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  grade!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  passingYear!: string;

  // Additional fields for more detailed academic records
  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true
  })
  cgpa?: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true
  })
  percentage?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  division?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  rollNumber?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  registrationNumber?: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true
  })
  examDate?: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  certificateNumber?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  notes?: string;

  // Document attachments (JSON array of file paths/URLs)
  @Column({
    type: DataType.JSON,
    allowNull: true,
    defaultValue: []
  })
  documents?: string[];

  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  metadata?: Record<string, any>;

  @BelongsTo(() => Student, { foreignKey: 'studentId', targetKey: 'studentId' })
  student!: Student;

  // Virtual properties
  get isHighSchool(): boolean {
    const highSchoolExams = ['SSC', 'HSC', 'O Level', 'A Level', 'High School'];
    return highSchoolExams.some((exam) =>
      this.nameOfExam.toLowerCase().includes(exam.toLowerCase())
    );
  }

  get isBachelor(): boolean {
    const bachelorExams = ['Bachelor', 'BSc', 'BA', 'BBA', 'BEng', 'BTech'];
    return bachelorExams.some((exam) =>
      this.nameOfExam.toLowerCase().includes(exam.toLowerCase())
    );
  }

  get isMaster(): boolean {
    const masterExams = ['Master', 'MSc', 'MA', 'MBA', 'MEng', 'MTech'];
    return masterExams.some((exam) =>
      this.nameOfExam.toLowerCase().includes(exam.toLowerCase())
    );
  }

  get isPhd(): boolean {
    const phdExams = ['PhD', 'Doctorate', 'DPhil'];
    return phdExams.some((exam) =>
      this.nameOfExam.toLowerCase().includes(exam.toLowerCase())
    );
  }

  get academicLevel(): string {
    if (this.isPhd) return 'doctorate';
    if (this.isMaster) return 'master';
    if (this.isBachelor) return 'bachelor';
    if (this.isHighSchool) return 'high_school';
    return 'other';
  }

  get hasDocuments(): boolean {
    return this.documents && this.documents.length > 0;
  }
}
