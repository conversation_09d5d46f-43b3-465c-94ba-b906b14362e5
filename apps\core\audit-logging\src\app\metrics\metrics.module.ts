import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';

import { MetricsController } from './metrics.controller';
import { MetricsMiddleware } from './metrics.middleware';
import { GrpcMetricsInterceptor } from './grpc-metrics.interceptor';
import { TracingInterceptor } from './tracing.interceptor';
import { MonitoringModule } from '@apply-goal-backend/monitoring';

@Module({
  imports: [
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'audit-logging',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '3001', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development',
          service_type: 'core'
        }
      },
      tracing: {
        serviceName: 'audit-logging',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces'
      }
    }),
  ],
  controllers: [MetricsController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: GrpcMetricsInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TracingInterceptor,
    }
  ],
})
export class MetricsModule {}
