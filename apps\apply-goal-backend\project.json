{"name": "apply-goal-backend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/apply-goal-backend/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "apply-goal-backend:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "apply-goal-backend:build:development"}, "production": {"buildTarget": "apply-goal-backend:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}