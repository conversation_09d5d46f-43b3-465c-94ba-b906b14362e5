// permissions.guard.ts
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RoleService } from '../role/role.service'; // this service now checks subAction strings
import { PERMISSIONS_KEY } from '../role/roles.decorator';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly roleService: RoleService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()]
    );
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Direct user permissions
    const userPermissions: string[] = user.permissions || [];

    // Department-level permissions
    const deptPermissions: string[] = [];
    if (Array.isArray(user.departments)) {
      for (const dept of user.departments) {
        // Feature permissions
        if (Array.isArray(dept.features)) {
          for (const feat of dept.features) {
            if (feat.DepartmentFeaturePermission?.isAllowed) {
              deptPermissions.push(`${feat.module?.name}:${feat.name}`);
            }
          }
        }
      }
    }

    // Combine all permissions
    const allPermissions = [...userPermissions, ...deptPermissions];

    const hasPerm = await this.roleService.validateUserPermissions(
      allPermissions,
      requiredPermissions
    );
    // Logger.debug(
    //   `PermissionsGuard: hasPerm: ${hasPerm}, allPermissions: ${JSON.stringify(
    //     allPermissions, 
    //     null,
    //     2
    //   )}, requiredPermissions: ${JSON.stringify(requiredPermissions, null, 2)}`
    // );

    if (!hasPerm) {
      throw new ForbiddenException('Insufficient permission privileges');
    }
    return true;
  }
}
