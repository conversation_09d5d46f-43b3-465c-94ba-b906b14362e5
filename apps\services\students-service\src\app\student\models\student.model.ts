import { BaseModel } from '@apply-goal-backend/database';
import {
  Column,
  DataType,
  HasMany,
  HasOne,
  BelongsTo,
  Table,
  Index,
  Default
} from 'sequelize-typescript';
import { EmergencyContact } from './emergency-contact.model';
import { Enrollment } from './enrollment.model';
import { Grade } from './grade.model';
import { StudentPersonalInfo } from './student-personal-info.model';
import { StudentAcademicRecord } from './student-academic-record.model';
import { StudentProficiencyRecord } from './student-proficiency-record.model';
import { StudentPublication } from './student-publication.model';
import { StudentSocialLink } from './student-social-link.model';
import { StudentOtherActivity } from './student-other-activity.model';
import { StudentAcademicBackground } from './student-academic.model';

@Table({
  tableName: 'students',
  indexes: [{ unique: true, fields: ['studentId'] }]
})
export class Student extends BaseModel {
  @Column({
    type: DataType.BIGINT,
    allowNull: true
  })
  userId?: bigint;

  @Column({
    type: DataType.STRING,
    unique: true,
    allowNull: false
  })
  studentId!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'active'
  })
  status!: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    comment:
      'Organization ID - always required (ApplyGoal or agency organization)'
  })
  organizationId: bigint;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Agency ID - optional, null if student not under any agency'
  })
  agencyId?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true
  })
  metadata?: Record<string, any>;

  // Relationships
  @HasOne(() => StudentPersonalInfo, { foreignKey: 'studentId' })
  personalInfo: StudentPersonalInfo;

  @HasMany(() => StudentAcademicRecord, { foreignKey: 'studentId' })
  academicRecords: StudentAcademicRecord[];

  @HasMany(() => StudentProficiencyRecord, { foreignKey: 'studentId' })
  proficiencyRecords: StudentProficiencyRecord[];

  @HasMany(() => StudentPublication, { foreignKey: 'studentId' })
  publications: StudentPublication[];

  @HasMany(() => StudentSocialLink, { foreignKey: 'studentId' })
  socialLinks: StudentSocialLink[];

  @HasMany(() => StudentOtherActivity, { foreignKey: 'studentId' })
  otherActivities: StudentOtherActivity[];

  @HasMany(() => Enrollment, { foreignKey: 'studentId' })
  enrollments: Enrollment[];

  @HasMany(() => StudentAcademicBackground, { foreignKey: 'studentId' })
  academicBackgrounds: StudentAcademicBackground[];

  @HasMany(() => Grade, { foreignKey: 'studentId' })
  grades: Grade[];

  @HasMany(() => EmergencyContact, { foreignKey: 'studentId' })
  emergency_contacts: EmergencyContact[];

  // Organization relationship - handled at service level
  // organizationId field is used to link to organizations table

  // Virtual properties
  get full_name(): string {
    return this.personalInfo
      ? `${this.personalInfo.firstName} ${this.personalInfo.lastName}`
      : 'Unknown';
  }

  get is_active(): boolean {
    return this.status === 'active';
  }

  get is_graduated(): boolean {
    return this.status === 'graduated';
  }

  get age(): number {
    if (!this.personalInfo?.dateOfBirth) return 0;
    const today = new Date();
    const birthDate = new Date(this.personalInfo.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }

  // Organization-related virtual properties
  get isUnderAgency(): boolean {
    return !!this.agencyId;
  }

  get isUnderApplyGoal(): boolean {
    return !this.agencyId; // If no agency, student is under ApplyGoal
  }

  get organizationName(): string {
    // Organization name will be fetched at service level
    return 'Organization Name (fetched at service level)';
  }

  get organizationType(): string {
    // Organization type will be fetched at service level
    return 'unknown';
  }
}
