const { Client } = require('@grpc/grpc-js');
const { loadPackageDefinition } = require('@grpc/grpc-js');
const { loadSync } = require('@grpc/proto-loader');
const path = require('path');

// Load the proto file
const PROTO_PATH = path.join(__dirname, 'libs/shared/dto/src/lib/students/students.proto');
const packageDefinition = loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true
});

const protoDescriptor = loadPackageDefinition(packageDefinition);
const students = protoDescriptor.students;

// Create client
const client = new students.StudentService(
  'localhost:50058',
  require('@grpc/grpc-js').credentials.createInsecure()
);

// Test the getStudentAcademic method
function testGetStudentAcademic() {
  return new Promise((resolve, reject) => {
    client.getStudentAcademic({ student_id: 'ST255292' }, (error, response) => {
      if (error) {
        console.error('Error:', error);
        reject(error);
      } else {
        console.log('Response:', JSON.stringify(response, null, 2));
        resolve(response);
      }
    });
  });
}

// Test the createOrUpdateStudentAcademic method
function testCreateOrUpdateStudentAcademic() {
  const academicData = {
    student_id: 'ST255292',
    academic_records: [
      {
        foreign_degree: false,
        name_of_exam: 'HSC',
        institute: 'Dhaka College',
        subject: 'Science',
        board: 'Dhaka',
        grade: 'A+',
        passing_year: '2013'
      }
    ],
    proficiency_records: [
      {
        name_of_exam: 'IELTS',
        score: {
          overall: 7.5,
          r: 8.0,
          l: 7.5,
          w: 7.0,
          s: 7.5
        },
        exam_date: '2023-06-15',
        expiry_date: '2025-06-15',
        note: 'Academic module'
      }
    ],
    publications: [
      {
        subject: 'Machine Learning',
        journal: 'IEEE Transactions',
        publication_date: '2023-01-15',
        link: 'https://ieee.org/paper123'
      }
    ],
    otherActivities: [
      {
        subject: 'Web Development',
        certification_link: 'https://cert.example.com/web-dev',
        start_date: '2023-01-01',
        end_date: '2023-06-30'
      }
    ]
  };

  return new Promise((resolve, reject) => {
    client.createOrUpdateStudentAcademic(academicData, (error, response) => {
      if (error) {
        console.error('Error:', error);
        reject(error);
      } else {
        console.log('Response:', JSON.stringify(response, null, 2));
        resolve(response);
      }
    });
  });
}

// Run tests
async function runTests() {
  try {
    console.log('Testing GET student academic...');
    await testGetStudentAcademic();
    
    console.log('\nTesting POST student academic...');
    await testCreateOrUpdateStudentAcademic();
    
    console.log('\nTesting GET student academic again...');
    await testGetStudentAcademic();
  } catch (error) {
    console.error('Test failed:', error);
  }
}

runTests(); 