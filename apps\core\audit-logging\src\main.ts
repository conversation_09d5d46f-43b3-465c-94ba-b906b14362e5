/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { join } from 'path';
import express from 'express';
import { healthRouter } from './app/health/health.routes';
import { MetricsService, TracingService } from '@apply-goal-backend/monitoring';
import { MigrationService } from './app/migration/migration.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  // Run database migrations on startup
  try {
    const migrationService = app.get(MigrationService);
    await migrationService.migrate();
    Logger.log('Database migrations completed successfully');
  } catch (error) {
    Logger.error('Failed to run migrations on startup', error);
    // Don't exit - allow the app to start even if migrations fail
    // This prevents container restart loops in production
  }

  // Setup gRPC Microservice with proper configuration
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'audit',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/audit/audit.proto'
      ),
      url: '0.0.0.0:50051',
    },
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  const port = process.env.PORT || 3001;

  // Start microservices
  await app.startAllMicroservices();

  await app.listen(port);
  Logger.log(
    `🚀 Audit Logging Service is running on: http://localhost:${port}/${globalPrefix}`
  );
  Logger.log(`🚀 gRPC server is running on: 0.0.0.0:50051`);
  Logger.log(
    `📊 Metrics server is running on: http://localhost:${port}/api/metrics`
  );
}

bootstrap();
